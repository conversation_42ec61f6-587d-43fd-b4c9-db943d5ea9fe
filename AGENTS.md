# Development Guide for AI Agents

## Build/Test/Lint Commands

### Python Projects (mcp-agent)
- `make sync` - Install dependencies with uv
- `make tests` - Run pytest tests
- `make lint` - Lint with ruff (--fix)
- `make format` - Format code with scripts
- `make coverage` - Run tests with coverage report
- `uv run pytest path/to/test_file.py::test_name` - Run single test

### TypeScript Projects (ag-ui)
- `pnpm run build` - Build all packages with turbo
- `pnpm run test` - Run all tests
- `pnpm run lint` - Lint all packages
- `pnpm run check-types` - TypeScript type checking
- `pnpm run dev` - Start development server

## Architecture Overview

Multi-language ecosystem focused on Model Context Protocol (MCP) agents:
- **Core MCP**: `mcp-agent/` (Python framework), `fastmcp/` (Python server/client), `python-sdk/` (official Python MCP)
- **Agent Frameworks**: `Qwen-Agent/`, `atomic-agents/`, `CopilotKit/` (React/TypeScript)
- **UI Components**: TypeScript monorepos using pnpm/turbo, React-based

## Code Style Guidelines

- **Python**: Use type hints, Pydantic for validation, async/await patterns, dataclasses for config
- **Imports**: Absolute imports preferred, graceful fallbacks for optional dependencies
- **Error Handling**: Use logging with structured messages, try/except with specific exceptions
- **Naming**: snake_case for Python, camelCase for TypeScript, descriptive variable names
- **Documentation**: Docstrings for modules/classes, inline comments only when necessary
