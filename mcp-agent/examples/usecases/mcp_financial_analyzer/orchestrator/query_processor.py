"""
Query Processing Module for Financial Orchestrator
================================================

Intelligent query parsing and routing system that analyzes complex financial
queries and determines the optimal workflow execution strategy.

Features:
- Natural language query parsing and classification
- Entity extraction (orders, materials, suppliers, customers)
- Parameter extraction (dates, quantities, thresholds)
- Workflow pattern matching and routing decisions
- Query ambiguity detection and clarification handling
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, date

from mcp_agent.logging.logger import get_logger

logger = get_logger(__name__)


class QueryType(Enum):
    """Financial query classification types."""
    SHORTAGE_ANALYSIS = "shortage_analysis"
    SUPPLIER_RISK = "supplier_risk"
    CUSTOMER_PRIORITY = "customer_priority"  
    INVENTORY_CHECK = "inventory_check"
    ORDER_STATUS = "order_status"
    PRODUCTION_PLANNING = "production_planning"
    COST_ANALYSIS = "cost_analysis"
    COMPREHENSIVE = "comprehensive"
    UNKNOWN = "unknown"


class WorkflowPattern(Enum):
    """Predefined workflow execution patterns."""
    MYSQL_ONLY = "mysql_only"  # MySQL → Results
    MYSQL_SHORTAGE = "mysql_shortage"  # MySQL → Shortage → Results
    MYSQL_ALERT = "mysql_alert"  # MySQL → Alert → Results
    FULL_WORKFLOW = "full_workflow"  # MySQL → Shortage → Alert → Results
    SHORTAGE_ONLY = "shortage_only"  # Shortage → Results (with synthetic data)
    ALERT_ONLY = "alert_only"  # Alert → Results (with synthetic data)


@dataclass
class EntityCollection:
    """Collection of extracted entities from query."""
    orders: List[str] = field(default_factory=list)  # CUSTORD-YYYYMMXXX
    work_orders: List[str] = field(default_factory=list)  # WO-YYYYMMXXX
    purchase_requests: List[str] = field(default_factory=list)  # PR-YYYYMMXXX
    materials: List[str] = field(default_factory=list)  # Component codes
    suppliers: List[str] = field(default_factory=list)  # Supplier names/codes
    customers: List[str] = field(default_factory=list)  # Customer names
    factories: List[str] = field(default_factory=list)  # Factory identifiers
    products: List[str] = field(default_factory=list)  # Product model names
    
    def has_entities(self) -> bool:
        """Check if any entities were found."""
        return any([
            self.orders, self.work_orders, self.purchase_requests,
            self.materials, self.suppliers, self.customers,
            self.factories, self.products
        ])
    
    def get_all_entities(self) -> Dict[str, List[str]]:
        """Get all entities as a dictionary."""
        return {
            'orders': self.orders,
            'work_orders': self.work_orders, 
            'purchase_requests': self.purchase_requests,
            'materials': self.materials,
            'suppliers': self.suppliers,
            'customers': self.customers,
            'factories': self.factories,
            'products': self.products
        }


@dataclass
class QueryParameters:
    """Extracted parameters from financial queries."""
    quantities: List[int] = field(default_factory=list)
    dates: List[str] = field(default_factory=list)
    thresholds: Dict[str, float] = field(default_factory=dict)
    priorities: List[str] = field(default_factory=list)
    time_ranges: List[str] = field(default_factory=list)
    risk_levels: List[str] = field(default_factory=list)
    currencies: List[str] = field(default_factory=list)
    percentages: List[float] = field(default_factory=list)


@dataclass 
class ParsedQuery:
    """Complete parsed query structure."""
    original_query: str
    query_type: QueryType
    confidence: float  # 0.0-1.0 confidence in classification
    workflow_pattern: WorkflowPattern
    entities: EntityCollection
    parameters: QueryParameters
    keywords: List[str] = field(default_factory=list)
    intent_keywords: List[str] = field(default_factory=list)
    complexity_score: float = 0.0
    ambiguity_flags: List[str] = field(default_factory=list)
    suggested_clarifications: List[str] = field(default_factory=list)


class FinancialQueryProcessor:
    """Advanced query processor for financial analysis requests."""
    
    def __init__(self):
        """Initialize the query processor with pattern definitions."""
        self._setup_patterns()
        self._setup_keywords()
        logger.info("FinancialQueryProcessor initialized")
    
    def _setup_patterns(self):
        """Setup regex patterns for entity and parameter extraction."""
        # Entity patterns
        self.patterns = {
            'orders': [
                r'CUSTORD-\d{9}',  # Standard customer order format
                r'order\s+(?:number\s+)?([A-Z0-9-]{8,15})',  # Generic order references
            ],
            'work_orders': [
                r'WO-\d{9}',  # Standard work order format
                r'work\s+order\s+([A-Z0-9-]{8,15})',
            ],
            'purchase_requests': [
                r'PR-\d{9}',  # Standard purchase request format
                r'purchase\s+request\s+([A-Z0-9-]{8,15})',
            ],
            'materials': [
                r'[A-Z]{2,4}\d{4,6}(?:[A-Z]?\d*)?',  # MM2004, HCS500, DEP9005, etc.
                r'[A-Z]+_[A-Z0-9_]+',  # DDR5_32GB, KCS_1TB, etc.
                r'\b(?:GPU|CPU|RAM|SSD|PSU|motherboard|fans?)\b',  # Generic component types
            ],
            'suppliers': [
                r'(?:MetaMind|Dyneon|AVATA|Kernesis|DeLite)\s+Technology',
                r'(?:Tech\s+Pioneer|Innovate\s+Electronics|QCT\s+Technology)\s+Co\.?',
            ],
            'customers': [
                r'Tech\s+Pioneer(?:\s+Co\.?)?',
                r'Innovate\s+Electronics(?:\s+Co\.?)?',
                r'QCT\s+Technology(?:\s+Co\.?)?',
            ],
            'factories': [
                r'Factory\s+[A-Z]',
                r'Manufacturing\s+Plant\s+\d+',
            ],
            'products': [
                r'G7B\s+Golden_\d+',
                r'G8D\s+LGD_\d+',
                r'[A-Z0-9]+\s+server',
            ],
        }
        
        # Parameter patterns
        self.param_patterns = {
            'quantities': [
                r'(\d+(?:,\d{3})*)\s*(?:units?|pieces?|items?|pcs?)',
                r'require[sd]?\s*(?:is\s*)?(\d+(?:,\d{3})*)',
                r'available\s*(?:is\s*)?(\d+(?:,\d{3})*)',
                r'stock\s*(?:level\s*)?(?:is\s*)?(\d+(?:,\d{3})*)',
            ],
            'dates': [
                r'\d{4}-\d{1,2}-\d{1,2}',  # YYYY-MM-DD
                r'\d{1,2}[/-]\d{1,2}[/-]\d{4}',  # MM/DD/YYYY or MM-DD-YYYY
                r'(?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2},?\s+\d{4}',
                r'(?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s+\d{1,2},?\s+\d{4}',
            ],
            'percentages': [
                r'(\d+(?:\.\d+)?)%',
                r'(\d+(?:\.\d+)?)\s*percent',
            ],
            'currencies': [
                r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)',  # USD
                r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:USD|dollars?)',
            ],
        }
    
    def _setup_keywords(self):
        """Setup keyword classifications for query type detection."""
        self.keywords = {
            QueryType.SHORTAGE_ANALYSIS: [
                'shortage', 'shortages', 'out of stock', 'inventory', 'stock level',
                'availability', 'available', 'shortage analysis', 'supply risk',
                'material shortage', 'component shortage', 'inventory risk'
            ],
            QueryType.SUPPLIER_RISK: [
                'supplier', 'vendor', 'delivery', 'procurement', 'sourcing',
                'supplier risk', 'vendor reliability', 'delivery performance',
                'supplier analysis', 'supply chain risk', 'vendor assessment'
            ],
            QueryType.CUSTOMER_PRIORITY: [
                'customer', 'priority', 'delivery date', 'order priority',
                'customer priority', 'delivery schedule', 'order fulfillment',
                'customer satisfaction', 'delivery commitment', 'priority order'
            ],
            QueryType.INVENTORY_CHECK: [
                'inventory check', 'stock check', 'material availability',
                'inventory status', 'stock status', 'current stock',
                'inventory level', 'stock inquiry'
            ],
            QueryType.ORDER_STATUS: [
                'order status', 'order inquiry', 'order tracking',
                'delivery status', 'order progress', 'fulfillment status',
                'shipping status', 'order information'
            ],
            QueryType.PRODUCTION_PLANNING: [
                'production', 'manufacturing', 'capacity', 'planning',
                'production schedule', 'manufacturing plan', 'capacity planning',
                'production analysis', 'work order', 'factory capacity'
            ],
            QueryType.COST_ANALYSIS: [
                'cost', 'price', 'budget', 'financial impact',
                'cost analysis', 'pricing', 'cost assessment',
                'financial analysis', 'budget impact', 'cost evaluation'
            ],
        }
        
        # Intent keywords for workflow routing
        self.intent_keywords = {
            'analysis_only': ['analyze', 'assess', 'evaluate', 'review', 'check'],
            'alert_required': ['alert', 'notify', 'warn', 'urgent', 'critical', 'immediate'],
            'reporting': ['report', 'summary', 'dashboard', 'status report'],
            'planning': ['plan', 'schedule', 'forecast', 'predict'],
        }
    
    def process_query(self, query: str) -> ParsedQuery:
        """
        Process a financial query and return structured analysis.
        
        Args:
            query: Natural language financial analysis query
            
        Returns:
            ParsedQuery with complete analysis results
        """
        logger.info(f"Processing financial query: {query[:100]}...")
        
        # Initialize parsed query structure
        parsed = ParsedQuery(
            original_query=query,
            query_type=QueryType.UNKNOWN,
            confidence=0.0,
            workflow_pattern=WorkflowPattern.FULL_WORKFLOW,
            entities=EntityCollection(),
            parameters=QueryParameters()
        )
        
        # Extract entities
        parsed.entities = self._extract_entities(query)
        
        # Extract parameters
        parsed.parameters = self._extract_parameters(query)
        
        # Classify query type
        parsed.query_type, parsed.confidence = self._classify_query_type(query)
        
        # Extract keywords
        parsed.keywords = self._extract_keywords(query)
        parsed.intent_keywords = self._extract_intent_keywords(query)
        
        # Determine workflow pattern
        parsed.workflow_pattern = self._determine_workflow_pattern(parsed)
        
        # Calculate complexity
        parsed.complexity_score = self._calculate_complexity(parsed)
        
        # Detect ambiguities
        parsed.ambiguity_flags = self._detect_ambiguities(parsed)
        parsed.suggested_clarifications = self._generate_clarifications(parsed)
        
        logger.info(f"Query classified as {parsed.query_type.value} "
                   f"(confidence: {parsed.confidence:.2f}, "
                   f"workflow: {parsed.workflow_pattern.value})")
        
        return parsed
    
    def _extract_entities(self, query: str) -> EntityCollection:
        """Extract named entities from the query."""
        entities = EntityCollection()
        
        # Extract each entity type
        for entity_type, patterns in self.patterns.items():
            matches = []
            for pattern in patterns:
                matches.extend(re.findall(pattern, query, re.IGNORECASE))
            
            # Clean and deduplicate matches
            matches = list(set([match.strip() for match in matches if match.strip()]))
            
            # Assign to appropriate entity list
            if entity_type == 'orders':
                entities.orders = matches
            elif entity_type == 'work_orders':
                entities.work_orders = matches
            elif entity_type == 'purchase_requests':
                entities.purchase_requests = matches
            elif entity_type == 'materials':
                entities.materials = matches
            elif entity_type == 'suppliers':
                entities.suppliers = matches
            elif entity_type == 'customers':
                entities.customers = matches
            elif entity_type == 'factories':
                entities.factories = matches
            elif entity_type == 'products':
                entities.products = matches
        
        return entities
    
    def _extract_parameters(self, query: str) -> QueryParameters:
        """Extract numerical and date parameters from the query."""
        params = QueryParameters()
        
        # Extract quantities
        for pattern in self.param_patterns['quantities']:
            matches = re.findall(pattern, query, re.IGNORECASE)
            for match in matches:
                try:
                    # Remove commas and convert to int
                    qty = int(match.replace(',', ''))
                    params.quantities.append(qty)
                except ValueError:
                    continue
        
        # Extract dates
        for pattern in self.param_patterns['dates']:
            matches = re.findall(pattern, query, re.IGNORECASE)
            params.dates.extend(matches)
        
        # Extract percentages
        for pattern in self.param_patterns['percentages']:
            matches = re.findall(pattern, query, re.IGNORECASE)
            for match in matches:
                try:
                    pct = float(match)
                    params.percentages.append(pct)
                except ValueError:
                    continue
        
        # Extract currency amounts
        for pattern in self.param_patterns['currencies']:
            matches = re.findall(pattern, query, re.IGNORECASE)
            params.currencies.extend(matches)
        
        # Extract priority indicators
        priority_patterns = [
            r'\b(high|medium|low)\s+priority\b',
            r'\b(urgent|critical|normal|routine)\b',
            r'\b(immediate|asap|rush)\b'
        ]
        
        for pattern in priority_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            params.priorities.extend(matches)
        
        # Remove duplicates
        params.dates = list(set(params.dates))
        params.priorities = list(set(params.priorities))
        params.currencies = list(set(params.currencies))
        
        return params
    
    def _classify_query_type(self, query: str) -> Tuple[QueryType, float]:
        """Classify the query type and return confidence score."""
        query_lower = query.lower()
        
        # Calculate keyword match scores for each query type
        type_scores = {}
        
        for query_type, keywords in self.keywords.items():
            score = 0.0
            matches = 0
            
            for keyword in keywords:
                if keyword.lower() in query_lower:
                    matches += 1
                    # Weight longer keywords more heavily
                    score += len(keyword.split()) * 1.0
            
            # Calculate confidence based on matches and keyword density
            if matches > 0:
                type_scores[query_type] = score / len(keywords)
        
        # If no specific type detected, check for comprehensive indicators
        if not type_scores:
            comprehensive_indicators = [
                'analysis', 'assess', 'evaluate', 'comprehensive',
                'end-to-end', 'full analysis', 'complete'
            ]
            
            comp_score = sum(1 for indicator in comprehensive_indicators 
                           if indicator in query_lower)
            
            if comp_score > 0:
                return QueryType.COMPREHENSIVE, min(comp_score * 0.3, 0.9)
            else:
                return QueryType.UNKNOWN, 0.1
        
        # Return the highest scoring type
        best_type = max(type_scores, key=type_scores.get)
        confidence = min(type_scores[best_type], 1.0)
        
        return best_type, confidence
    
    def _extract_keywords(self, query: str) -> List[str]:
        """Extract relevant keywords from the query."""
        query_lower = query.lower()
        keywords = []
        
        # Extract all matched keywords from our definitions
        for query_type, keyword_list in self.keywords.items():
            for keyword in keyword_list:
                if keyword.lower() in query_lower:
                    keywords.append(keyword)
        
        return list(set(keywords))
    
    def _extract_intent_keywords(self, query: str) -> List[str]:
        """Extract intent-indicating keywords."""
        query_lower = query.lower()
        intent_keywords = []
        
        for intent, keyword_list in self.intent_keywords.items():
            for keyword in keyword_list:
                if keyword.lower() in query_lower:
                    intent_keywords.append(f"{intent}:{keyword}")
        
        return intent_keywords
    
    def _determine_workflow_pattern(self, parsed: ParsedQuery) -> WorkflowPattern:
        """Determine the appropriate workflow pattern based on parsed query."""
        query_type = parsed.query_type
        intent_keywords = parsed.intent_keywords
        entities = parsed.entities
        
        # Check for alert requirements
        needs_alerts = any('alert_required' in keyword for keyword in intent_keywords)
        
        # Check for data analysis requirements
        needs_mysql = (
            entities.orders or entities.work_orders or entities.purchase_requests or
            entities.suppliers or entities.customers or entities.factories or
            'MySQL' in parsed.original_query or
            query_type in [QueryType.SUPPLIER_RISK, QueryType.CUSTOMER_PRIORITY, QueryType.ORDER_STATUS]
        )
        
        # Check for shortage analysis requirements
        needs_shortage = (
            query_type == QueryType.SHORTAGE_ANALYSIS or
            'shortage' in parsed.original_query.lower() or
            'inventory' in parsed.original_query.lower() or
            entities.materials
        )
        
        # Determine pattern based on requirements
        if needs_mysql and needs_shortage and needs_alerts:
            return WorkflowPattern.FULL_WORKFLOW
        elif needs_mysql and needs_shortage:
            return WorkflowPattern.MYSQL_SHORTAGE
        elif needs_mysql and needs_alerts:
            return WorkflowPattern.MYSQL_ALERT
        elif needs_mysql:
            return WorkflowPattern.MYSQL_ONLY
        elif needs_shortage:
            return WorkflowPattern.SHORTAGE_ONLY
        elif needs_alerts:
            return WorkflowPattern.ALERT_ONLY
        else:
            # Default to comprehensive analysis
            return WorkflowPattern.FULL_WORKFLOW
    
    def _calculate_complexity(self, parsed: ParsedQuery) -> float:
        """Calculate query complexity score (0.0-1.0)."""
        complexity = 0.0
        
        # Entity complexity
        entity_count = sum(len(entity_list) for entity_list in parsed.entities.get_all_entities().values())
        complexity += min(entity_count * 0.1, 0.3)
        
        # Parameter complexity
        param_count = (len(parsed.parameters.quantities) + len(parsed.parameters.dates) +
                      len(parsed.parameters.priorities) + len(parsed.parameters.currencies))
        complexity += min(param_count * 0.05, 0.2)
        
        # Query length complexity
        word_count = len(parsed.original_query.split())
        complexity += min(word_count * 0.01, 0.2)
        
        # Multi-type indicators
        if parsed.query_type == QueryType.COMPREHENSIVE:
            complexity += 0.2
        
        # Ambiguity penalty
        complexity += len(parsed.ambiguity_flags) * 0.05
        
        return min(complexity, 1.0)
    
    def _detect_ambiguities(self, parsed: ParsedQuery) -> List[str]:
        """Detect potential ambiguities in the query."""
        ambiguities = []
        
        # Multiple entity types without clear relationships
        entity_types_found = sum(1 for entity_list in parsed.entities.get_all_entities().values() 
                                if entity_list)
        
        if entity_types_found > 3:
            ambiguities.append("multiple_entity_types")
        
        # Low classification confidence
        if parsed.confidence < 0.5:
            ambiguities.append("low_classification_confidence")
        
        # Contradictory intent keywords
        analysis_intents = [kw for kw in parsed.intent_keywords if 'analysis_only' in kw]
        alert_intents = [kw for kw in parsed.intent_keywords if 'alert_required' in kw]
        
        if analysis_intents and alert_intents:
            ambiguities.append("contradictory_intents")
        
        # Missing key information for classified type
        if parsed.query_type == QueryType.SHORTAGE_ANALYSIS and not parsed.entities.materials:
            ambiguities.append("missing_materials_for_shortage")
        
        if parsed.query_type == QueryType.SUPPLIER_RISK and not parsed.entities.suppliers:
            ambiguities.append("missing_suppliers_for_risk")
        
        return ambiguities
    
    def _generate_clarifications(self, parsed: ParsedQuery) -> List[str]:
        """Generate suggested clarifications based on detected ambiguities."""
        clarifications = []
        
        for ambiguity in parsed.ambiguity_flags:
            if ambiguity == "multiple_entity_types":
                clarifications.append(
                    "The query contains multiple types of entities. "
                    "Could you specify which entities are most important for the analysis?"
                )
            
            elif ambiguity == "low_classification_confidence":
                clarifications.append(
                    "The query type is unclear. Could you specify whether you want "
                    "shortage analysis, supplier risk assessment, or customer priority analysis?"
                )
            
            elif ambiguity == "contradictory_intents":
                clarifications.append(
                    "The query seems to request both analysis and alerts. "
                    "Should the system provide analysis only, or also send notifications?"
                )
            
            elif ambiguity == "missing_materials_for_shortage":
                clarifications.append(
                    "For shortage analysis, please specify which materials or components "
                    "you want to analyze."
                )
            
            elif ambiguity == "missing_suppliers_for_risk":
                clarifications.append(
                    "For supplier risk assessment, please specify which suppliers "
                    "you want to evaluate."
                )
        
        return clarifications
    
    def is_query_clear(self, parsed: ParsedQuery) -> bool:
        """Determine if the query is clear enough for execution."""
        return (
            # parsed.confidence >= 0.6 and  # Commented out for demo
            # len(parsed.ambiguity_flags) == 0 and  # Commented out for demo
            parsed.query_type != QueryType.UNKNOWN
        )
    
    def get_execution_plan(self, parsed: ParsedQuery) -> Dict[str, Any]:
        """Generate an execution plan based on parsed query."""
        plan = {
            "query_type": parsed.query_type.value,
            "workflow_pattern": parsed.workflow_pattern.value,
            "confidence": parsed.confidence,
            "complexity": parsed.complexity_score,
            "estimated_steps": self._estimate_steps(parsed.workflow_pattern),
            "required_agents": self._get_required_agents(parsed.workflow_pattern),
            "execution_order": self._get_execution_order(parsed.workflow_pattern),
            "context_sharing": True,
            "parallel_execution": False,  # Currently sequential
            "fallback_strategy": "graceful_degradation"
        }
        
        return plan
    
    def _estimate_steps(self, workflow_pattern: WorkflowPattern) -> int:
        """Estimate number of execution steps for workflow pattern."""
        step_count = {
            WorkflowPattern.MYSQL_ONLY: 1,
            WorkflowPattern.MYSQL_SHORTAGE: 2,
            WorkflowPattern.MYSQL_ALERT: 2,
            WorkflowPattern.FULL_WORKFLOW: 3,
            WorkflowPattern.SHORTAGE_ONLY: 1,
            WorkflowPattern.ALERT_ONLY: 1,
        }
        return step_count.get(workflow_pattern, 3)
    
    def _get_required_agents(self, workflow_pattern: WorkflowPattern) -> List[str]:
        """Get list of required agents for workflow pattern."""
        agent_map = {
            WorkflowPattern.MYSQL_ONLY: ["mysql_analyzer"],
            WorkflowPattern.MYSQL_SHORTAGE: ["mysql_analyzer", "shortage_analyzer"],
            WorkflowPattern.MYSQL_ALERT: ["mysql_analyzer", "alert_manager"],
            WorkflowPattern.FULL_WORKFLOW: ["mysql_analyzer", "shortage_analyzer", "alert_manager"],
            WorkflowPattern.SHORTAGE_ONLY: ["shortage_analyzer"],
            WorkflowPattern.ALERT_ONLY: ["alert_manager"],
        }
        return agent_map.get(workflow_pattern, ["mysql_analyzer", "shortage_analyzer", "alert_manager"])
    
    def _get_execution_order(self, workflow_pattern: WorkflowPattern) -> List[str]:
        """Get execution order for agents in workflow pattern."""
        # Same as required agents since we execute sequentially
        return self._get_required_agents(workflow_pattern)