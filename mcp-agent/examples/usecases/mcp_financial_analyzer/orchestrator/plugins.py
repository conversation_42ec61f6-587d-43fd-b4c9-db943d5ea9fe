"""
Orchestration Plugin Architecture for MCP Financial Analyzer
==========================================================

This module provides the plugin architecture for extending the orchestration system
with custom functionality, data transformations, and workflow enhancements.

Key Components:
- OrchestrationPlugin: Abstract base class for all plugins
- PluginManager: Manages plugin registration, discovery, and lifecycle
- Plugin lifecycle management and error handling
"""

import asyncio
import importlib
import importlib.util
import inspect
import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Type
from dataclasses import dataclass
from enum import Enum

from orchestrator.query_processor import QueryType
from orchestrator.exceptions import OrchestrationError

logger = logging.getLogger(__name__)


class PluginStatus(Enum):
    """Plugin status enumeration."""
    UNREGISTERED = "unregistered"
    REGISTERED = "registered"
    INITIALIZING = "initializing"
    INITIALIZED = "initialized"
    FAILED = "failed"
    CLEANUP = "cleanup"


@dataclass
class PluginInfo:
    """Plugin information and metadata."""
    name: str
    version: str
    description: str
    author: str
    supported_query_types: List[QueryType]
    dependencies: List[str]
    status: PluginStatus
    error_message: Optional[str] = None


class OrchestrationPlugin(ABC):
    """
    Abstract base class for orchestration plugins.
    
    Plugins extend the orchestration system with custom functionality,
    data transformations, workflow enhancements, and integration capabilities.
    """
    
    def __init__(self):
        """Initialize the plugin."""
        self._status = PluginStatus.UNREGISTERED
        self._error_message: Optional[str] = None
        self._orchestrator = None
    
    @property
    def status(self) -> PluginStatus:
        """Get current plugin status."""
        return self._status
    
    @property
    def error_message(self) -> Optional[str]:
        """Get error message if plugin failed."""
        return self._error_message
    
    @abstractmethod
    def get_name(self) -> str:
        """Get plugin name."""
        pass
    
    @abstractmethod
    def get_version(self) -> str:
        """Get plugin version."""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get plugin description."""
        pass
    
    @abstractmethod
    def get_author(self) -> str:
        """Get plugin author."""
        pass
    
    @abstractmethod
    def get_supported_query_types(self) -> List[QueryType]:
        """Get list of query types this plugin supports."""
        pass
    
    @abstractmethod
    def get_dependencies(self) -> List[str]:
        """Get list of plugin dependencies."""
        pass
    
    @abstractmethod
    async def initialize(self, orchestrator: Any) -> bool:
        """
        Initialize the plugin with orchestrator context.
        
        Args:
            orchestrator: The financial orchestrator instance
            
        Returns:
            True if initialization successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def process_data(self, data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process data during orchestration workflow.
        
        Args:
            data: Input data to process
            context: Workflow context information
            
        Returns:
            Processed data dictionary
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup plugin resources."""
        pass
    
    def get_info(self) -> PluginInfo:
        """Get comprehensive plugin information."""
        return PluginInfo(
            name=self.get_name(),
            version=self.get_version(),
            description=self.get_description(),
            author=self.get_author(),
            supported_query_types=self.get_supported_query_types(),
            dependencies=self.get_dependencies(),
            status=self._status,
            error_message=self._error_message
        )
    
    async def _safe_initialize(self, orchestrator: Any) -> bool:
        """Safely initialize plugin with error handling."""
        try:
            self._status = PluginStatus.INITIALIZING
            self._orchestrator = orchestrator
            result = await self.initialize(orchestrator)
            
            if result:
                self._status = PluginStatus.INITIALIZED
                logger.info(f"Plugin '{self.get_name()}' initialized successfully")
            else:
                self._status = PluginStatus.FAILED
                self._error_message = "Plugin initialization returned False"
                logger.error(f"Plugin '{self.get_name()}' initialization failed")
            
            return result
            
        except Exception as e:
            self._status = PluginStatus.FAILED
            self._error_message = str(e)
            logger.error(f"Plugin '{self.get_name()}' initialization error: {e}")
            return False
    
    async def _safe_cleanup(self) -> None:
        """Safely cleanup plugin with error handling."""
        try:
            self._status = PluginStatus.CLEANUP
            await self.cleanup()
            self._status = PluginStatus.UNREGISTERED
            logger.info(f"Plugin '{self.get_name()}' cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Plugin '{self.get_name()}' cleanup error: {e}")
            self._error_message = f"Cleanup error: {str(e)}"


class PluginManager:
    """
    Manages orchestration plugins including registration, discovery, and lifecycle.
    
    The PluginManager handles:
    - Plugin registration and unregistration
    - Dynamic plugin discovery from directories
    - Plugin lifecycle management (initialize, cleanup)
    - Dependency resolution
    - Error handling and recovery
    """
    
    def __init__(self):
        """Initialize the plugin manager."""
        self._plugins: Dict[str, OrchestrationPlugin] = {}
        self._plugin_order: List[str] = []
        self._initialized_plugins: Set[str] = set()
        self._failed_plugins: Set[str] = set()
        self._orchestrator = None
        
        logger.info("PluginManager initialized")
    
    def register_plugin(self, plugin: OrchestrationPlugin) -> bool:
        """
        Register a plugin with the manager.
        
        Args:
            plugin: Plugin instance to register
            
        Returns:
            True if registration successful, False otherwise
        """
        try:
            plugin_name = plugin.get_name()
            
            if plugin_name in self._plugins:
                logger.warning(f"Plugin '{plugin_name}' already registered, replacing")
            
            # Validate plugin
            if not self._validate_plugin(plugin):
                logger.error(f"Plugin '{plugin_name}' validation failed")
                return False
            
            self._plugins[plugin_name] = plugin
            plugin._status = PluginStatus.REGISTERED
            
            # Add to order if not already present
            if plugin_name not in self._plugin_order:
                self._plugin_order.append(plugin_name)
            
            logger.info(f"Plugin '{plugin_name}' registered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error registering plugin: {e}")
            return False
    
    def unregister_plugin(self, plugin_name: str) -> bool:
        """
        Unregister a plugin from the manager.
        
        Args:
            plugin_name: Name of plugin to unregister
            
        Returns:
            True if unregistration successful, False otherwise
        """
        try:
            if plugin_name not in self._plugins:
                logger.warning(f"Plugin '{plugin_name}' not found for unregistration")
                return False
            
            plugin = self._plugins[plugin_name]
            
            # Cleanup if initialized
            if plugin_name in self._initialized_plugins:
                asyncio.create_task(plugin._safe_cleanup())
                self._initialized_plugins.discard(plugin_name)
            
            # Remove from tracking
            del self._plugins[plugin_name]
            self._plugin_order.remove(plugin_name)
            self._failed_plugins.discard(plugin_name)
            
            logger.info(f"Plugin '{plugin_name}' unregistered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error unregistering plugin '{plugin_name}': {e}")
            return False
    
    def get_plugin(self, name: str) -> Optional[OrchestrationPlugin]:
        """Get plugin by name."""
        return self._plugins.get(name)
    
    def get_all_plugins(self) -> Dict[str, OrchestrationPlugin]:
        """Get all registered plugins."""
        return self._plugins.copy()
    
    def get_plugin_info(self, name: str) -> Optional[PluginInfo]:
        """Get plugin information by name."""
        plugin = self._plugins.get(name)
        return plugin.get_info() if plugin else None
    
    def get_all_plugin_info(self) -> List[PluginInfo]:
        """Get information for all registered plugins."""
        return [plugin.get_info() for plugin in self._plugins.values()]
    
    def get_plugins_for_query_type(self, query_type: QueryType) -> List[OrchestrationPlugin]:
        """
        Get plugins that support a specific query type.
        
        Args:
            query_type: Query type to filter by
            
        Returns:
            List of plugins supporting the query type
        """
        matching_plugins = []
        
        for plugin in self._plugins.values():
            try:
                if query_type in plugin.get_supported_query_types():
                    matching_plugins.append(plugin)
            except Exception as e:
                logger.error(f"Error checking query type support for plugin '{plugin.get_name()}': {e}")
        
        return matching_plugins
    
    async def initialize_all_plugins(self, orchestrator: Any) -> Dict[str, bool]:
        """
        Initialize all registered plugins.
        
        Args:
            orchestrator: The financial orchestrator instance
            
        Returns:
            Dictionary mapping plugin names to initialization success status
        """
        self._orchestrator = orchestrator
        results = {}
        
        logger.info(f"Initializing {len(self._plugins)} plugins")
        
        # Initialize plugins in registration order
        for plugin_name in self._plugin_order:
            if plugin_name in self._plugins:
                plugin = self._plugins[plugin_name]
                
                try:
                    success = await plugin._safe_initialize(orchestrator)
                    results[plugin_name] = success
                    
                    if success:
                        self._initialized_plugins.add(plugin_name)
                        self._failed_plugins.discard(plugin_name)
                    else:
                        self._failed_plugins.add(plugin_name)
                        
                except Exception as e:
                    logger.error(f"Error initializing plugin '{plugin_name}': {e}")
                    results[plugin_name] = False
                    self._failed_plugins.add(plugin_name)
        
        successful = sum(1 for success in results.values() if success)
        logger.info(f"Plugin initialization complete: {successful}/{len(results)} successful")
        
        return results
    
    async def cleanup_all_plugins(self) -> None:
        """Cleanup all initialized plugins."""
        logger.info(f"Cleaning up {len(self._initialized_plugins)} initialized plugins")
        
        cleanup_tasks = []
        for plugin_name in list(self._initialized_plugins):
            if plugin_name in self._plugins:
                plugin = self._plugins[plugin_name]
                cleanup_tasks.append(plugin._safe_cleanup())
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        self._initialized_plugins.clear()
        logger.info("Plugin cleanup complete")
    
    def load_plugins_from_directory(self, plugin_dir: Path) -> List[OrchestrationPlugin]:
        """
        Dynamically load plugins from a directory.

        Args:
            plugin_dir: Directory containing plugin modules

        Returns:
            List of successfully loaded plugins
        """
        loaded_plugins = []

        if not plugin_dir.exists() or not plugin_dir.is_dir():
            logger.warning(f"Plugin directory does not exist: {plugin_dir}")
            return loaded_plugins

        logger.info(f"Loading plugins from directory: {plugin_dir}")

        # Find all Python files in the directory
        for plugin_file in plugin_dir.glob("*.py"):
            if plugin_file.name.startswith("__"):
                continue  # Skip __init__.py and __pycache__

            try:
                # Import the module
                module_name = plugin_file.stem
                spec = importlib.util.spec_from_file_location(module_name, plugin_file)

                if spec is None or spec.loader is None:
                    logger.warning(f"Could not load spec for plugin: {plugin_file}")
                    continue

                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)

                # Find plugin classes in the module
                for name, obj in inspect.getmembers(module, inspect.isclass):
                    if (issubclass(obj, OrchestrationPlugin) and
                        obj != OrchestrationPlugin and
                        not inspect.isabstract(obj)):

                        try:
                            # Instantiate the plugin
                            plugin_instance = obj()

                            # Register the plugin
                            if self.register_plugin(plugin_instance):
                                loaded_plugins.append(plugin_instance)
                                logger.info(f"Loaded plugin '{plugin_instance.get_name()}' from {plugin_file}")
                            else:
                                logger.error(f"Failed to register plugin from {plugin_file}")

                        except Exception as e:
                            logger.error(f"Error instantiating plugin class '{name}' from {plugin_file}: {e}")

            except Exception as e:
                logger.error(f"Error loading plugin from {plugin_file}: {e}")

        logger.info(f"Loaded {len(loaded_plugins)} plugins from directory")
        return loaded_plugins

    def _validate_plugin(self, plugin: OrchestrationPlugin) -> bool:
        """Validate plugin implementation."""
        try:
            # Check required methods exist and are callable
            required_methods = [
                'get_name', 'get_version', 'get_description', 'get_author',
                'get_supported_query_types', 'get_dependencies',
                'initialize', 'process_data', 'cleanup'
            ]

            for method_name in required_methods:
                if not hasattr(plugin, method_name):
                    logger.error(f"Plugin missing required method: {method_name}")
                    return False

                method = getattr(plugin, method_name)
                if not callable(method):
                    logger.error(f"Plugin method '{method_name}' is not callable")
                    return False

            # Validate basic plugin info
            name = plugin.get_name()
            if not name or not isinstance(name, str):
                logger.error("Plugin name must be a non-empty string")
                return False

            version = plugin.get_version()
            if not version or not isinstance(version, str):
                logger.error("Plugin version must be a non-empty string")
                return False

            return True

        except Exception as e:
            logger.error(f"Plugin validation error: {e}")
            return False


# Global plugin manager instance
_plugin_manager: Optional[PluginManager] = None


def get_plugin_manager() -> PluginManager:
    """Get global plugin manager instance."""
    global _plugin_manager
    
    if _plugin_manager is None:
        _plugin_manager = PluginManager()
    
    return _plugin_manager
