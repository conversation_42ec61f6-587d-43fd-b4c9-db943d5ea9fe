{"permissions": {"allow": ["mcp__serena__list_dir", "Read(/merge/mcp-agent/examples/usecases/mcp_financial_analyzer/**)", "Read(/merge/mcp-agent/examples/usecases/mcp_financial_analyzer/**)", "mcp__serena__get_symbols_overview", "mcp__serena__find_symbol", "Read(/merge/mcp-agent/examples/usecases/mcp_financial_analyzer/**)", "mcp__serena__search_for_pattern", "mcp__serena__find_file", "<PERSON><PERSON>(python:*)", "Bash(timeout 30 python orchestrated_main.py)"], "deny": [], "ask": [], "additionalDirectories": ["/merge/mcp-agent/examples/usecases/mcp_financial_analyzer"]}}