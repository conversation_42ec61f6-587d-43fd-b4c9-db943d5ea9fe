#!/usr/bin/env python3
"""Debug plugin manager fixture issue."""

import pytest
import sys
import os

# Add project root to path
sys.path.append('.')

def test_plugin_manager_debug(plugin_manager):
    """Debug test to check plugin manager fixture."""
    print(f"plugin_manager type: {type(plugin_manager)}")
    print(f"plugin_manager: {plugin_manager}")
    
    if hasattr(plugin_manager, 'get_all_plugins'):
        plugins = plugin_manager.get_all_plugins()
        print(f"plugins: {plugins}")
        assert len(plugins) == 0
    else:
        print("plugin_manager does not have get_all_plugins method")
        print(f"Available methods: {[m for m in dir(plugin_manager) if not m.startswith('_')]}")

if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
