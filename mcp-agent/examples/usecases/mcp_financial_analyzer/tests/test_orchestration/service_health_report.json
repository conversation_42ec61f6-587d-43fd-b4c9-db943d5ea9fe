{"overall_health": {"all_services_healthy": true, "healthy_services": 4, "total_required_services": 4, "health_check_duration": 22.779069900512695, "timestamp": **********.9557896}, "service_results": {"shortage_service": {"service_name": "Shortage Index Service", "service_url": "http://localhost:6970", "overall_healthy": true, "required": true, "endpoints": {"/": {"healthy": true, "message": "Status 404, Response time: 0.007s", "details": {"status_code": 404, "response_time": 0.006531715393066406, "headers": {"Date": "Tu<PERSON>, 26 Aug 2025 02:11:42 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "9", "Content-Type": "text/plain; charset=utf-8"}, "content_preview": "Not Found"}}, "/health": {"healthy": true, "message": "Status 404, Response time: 0.002s", "details": {"status_code": 404, "response_time": 0.0020744800567626953, "headers": {"Date": "Tu<PERSON>, 26 Aug 2025 02:11:42 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "9", "Content-Type": "text/plain; charset=utf-8"}, "content_preview": "Not Found"}}, "/sse": {"healthy": true, "message": "Status 200, Response time: 0.005s", "details": {"status_code": 200, "response_time": 0.004835605621337891, "headers": {"Date": "Tu<PERSON>, 26 Aug 2025 02:11:42 GMT", "Server": "u<PERSON><PERSON>", "Cache-Control": "no-store", "Connection": "keep-alive", "x-accel-buffering": "no", "Content-Type": "text/event-stream; charset=utf-8", "Transfer-Encoding": "chunked"}, "content_preview": "Could not parse response content"}}}, "check_timestamp": **********.6179338}, "alert_service": {"service_name": "Alert Notification Service", "service_url": "http://localhost:6971", "overall_healthy": true, "required": true, "endpoints": {"/": {"healthy": true, "message": "Status 404, Response time: 0.003s", "details": {"status_code": 404, "response_time": 0.003033876419067383, "headers": {"Date": "Tu<PERSON>, 26 Aug 2025 02:11:51 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "9", "Content-Type": "text/plain; charset=utf-8"}, "content_preview": "Not Found"}}, "/health": {"healthy": true, "message": "Status 404, Response time: 0.002s", "details": {"status_code": 404, "response_time": 0.0017452239990234375, "headers": {"Date": "Tu<PERSON>, 26 Aug 2025 02:11:51 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "9", "Content-Type": "text/plain; charset=utf-8"}, "content_preview": "Not Found"}}, "/sse": {"healthy": true, "message": "Status 200, Response time: 0.005s", "details": {"status_code": 200, "response_time": 0.005204677581787109, "headers": {"Date": "Tu<PERSON>, 26 Aug 2025 02:11:51 GMT", "Server": "u<PERSON><PERSON>", "Cache-Control": "no-store", "Connection": "keep-alive", "x-accel-buffering": "no", "Content-Type": "text/event-stream; charset=utf-8", "Transfer-Encoding": "chunked"}, "content_preview": "Could not parse response content"}}}, "check_timestamp": **********.6200397}, "mysql_database": {"service_name": "MySQL Database", "overall_healthy": true, "required": true, "details": {"agent_creation_time": 0.0021686553955078125, "agent_type": "BaseAgent", "has_llm_init": false}, "check_timestamp": **********.955224}, "test_data": {"service_name": "Test Data Files", "overall_healthy": true, "required": true, "files": {"financial_queries.json": {"exists": true, "valid": true, "size_bytes": 4853, "data_keys": ["shortage_analysis_queries", "alert_management_queries", "comprehensive_analysis_queries", "ambiguous_queries", "invalid_queries", "performance_test_queries", "plugin_test_queries"], "data_count": 7}, "expected_results.json": {"exists": true, "valid": true, "size_bytes": 5666, "data_keys": ["shortage_analysis_results", "alert_management_results", "comprehensive_analysis_results", "plugin_processing_results", "error_response_structure", "performance_benchmarks", "data_validation_rules", "workflow_patterns", "test_data_samples"], "data_count": 9}}, "check_timestamp": **********.955772}}}