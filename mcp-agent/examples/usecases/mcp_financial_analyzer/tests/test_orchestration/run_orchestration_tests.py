#!/usr/bin/env python3
"""
Comprehensive test execution script for orchestration system tests.

This script runs all orchestration tests with proper setup, service validation,
and comprehensive reporting. It ensures all required services are running
and provides detailed test results and performance metrics.
"""

import asyncio
import sys
import os
import logging
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
import json

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('orchestration_test_results.log')
    ]
)
logger = logging.getLogger(__name__)


class OrchestrationTestRunner:
    """Comprehensive test runner for orchestration system."""
    
    def __init__(self):
        self.test_results = {}
        self.service_status = {}
        self.performance_metrics = {}
        self.start_time = None
        self.end_time = None
    
    async def check_service_availability(self) -> Dict[str, bool]:
        """Check if required services are available."""
        import aiohttp
        
        services = {
            "shortage_service": "http://localhost:6970",
            "alert_service": "http://localhost:6971"
        }
        
        service_status = {}
        
        async with aiohttp.ClientSession() as session:
            for service_name, url in services.items():
                try:
                    async with session.get(f"{url}/health", timeout=5) as response:
                        service_status[service_name] = response.status == 200
                        logger.info(f"Service {service_name} at {url}: {'Available' if service_status[service_name] else 'Unavailable'}")
                except Exception as e:
                    service_status[service_name] = False
                    logger.warning(f"Service {service_name} at {url}: Unavailable ({e})")
        
        self.service_status = service_status
        return service_status
    
    def run_pytest_tests(self, test_path: str, markers: Optional[List[str]] = None) -> Dict[str, Any]:
        """Run pytest tests and capture results."""
        cmd = ["python", "-m", "pytest", test_path, "-v", "--tb=short"]
        
        if markers:
            for marker in markers:
                cmd.extend(["-m", marker])
        
        # Add JSON report
        json_report_file = f"test_results_{int(time.time())}.json"
        cmd.extend(["--json-report", f"--json-report-file={json_report_file}"])
        
        logger.info(f"Running command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            # Parse JSON report if available
            test_data = {}
            if Path(json_report_file).exists():
                try:
                    with open(json_report_file, 'r') as f:
                        test_data = json.load(f)
                    os.remove(json_report_file)  # Cleanup
                except Exception as e:
                    logger.warning(f"Could not parse JSON report: {e}")
            
            return {
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "test_data": test_data,
                "success": result.returncode == 0
            }
            
        except subprocess.TimeoutExpired:
            logger.error(f"Test execution timed out for {test_path}")
            return {
                "returncode": -1,
                "stdout": "",
                "stderr": "Test execution timed out",
                "test_data": {},
                "success": False
            }
        except Exception as e:
            logger.error(f"Error running tests for {test_path}: {e}")
            return {
                "returncode": -1,
                "stdout": "",
                "stderr": str(e),
                "test_data": {},
                "success": False
            }
    
    async def run_plugin_manager_tests(self) -> Dict[str, Any]:
        """Run plugin manager integration tests."""
        logger.info("Running plugin manager tests...")
        
        test_path = "tests/test_orchestration/test_plugin_manager.py"
        markers = ["plugin_test"]
        
        result = self.run_pytest_tests(test_path, markers)
        self.test_results["plugin_manager"] = result
        
        return result
    
    async def run_orchestration_integration_tests(self) -> Dict[str, Any]:
        """Run orchestration integration tests."""
        logger.info("Running orchestration integration tests...")
        
        test_path = "tests/test_orchestration/test_orchestration_integration.py"
        markers = ["integration"]
        
        result = self.run_pytest_tests(test_path, markers)
        self.test_results["orchestration_integration"] = result
        
        return result
    
    async def run_end_to_end_tests(self) -> Dict[str, Any]:
        """Run end-to-end workflow tests."""
        logger.info("Running end-to-end workflow tests...")
        
        # Check service availability first
        service_status = await self.check_service_availability()
        if not all(service_status.values()):
            logger.warning("Some services are unavailable, E2E tests may be skipped")
        
        test_path = "tests/test_orchestration/test_end_to_end_workflows.py"
        markers = ["end_to_end", "real_service"]
        
        result = self.run_pytest_tests(test_path, markers)
        self.test_results["end_to_end"] = result
        
        return result
    
    async def run_error_handling_tests(self) -> Dict[str, Any]:
        """Run error handling and recovery tests."""
        logger.info("Running error handling tests...")
        
        test_path = "tests/test_orchestration/test_error_handling.py"
        markers = ["error_handling"]
        
        result = self.run_pytest_tests(test_path, markers)
        self.test_results["error_handling"] = result
        
        return result
    
    async def run_performance_tests(self) -> Dict[str, Any]:
        """Run performance and resource usage tests."""
        logger.info("Running performance tests...")
        
        # Run performance tests from all test files
        test_paths = [
            "tests/test_orchestration/test_plugin_manager.py",
            "tests/test_orchestration/test_orchestration_integration.py",
            "tests/test_orchestration/test_end_to_end_workflows.py"
        ]
        
        performance_results = {}
        
        for test_path in test_paths:
            markers = ["performance"]
            result = self.run_pytest_tests(test_path, markers)
            test_name = Path(test_path).stem
            performance_results[test_name] = result
        
        self.test_results["performance"] = performance_results
        return performance_results
    
    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        skipped_tests = 0
        
        for test_category, result in self.test_results.items():
            if isinstance(result, dict) and "test_data" in result:
                test_data = result["test_data"]
                if "summary" in test_data:
                    summary = test_data["summary"]
                    total_tests += summary.get("total", 0)
                    passed_tests += summary.get("passed", 0)
                    failed_tests += summary.get("failed", 0)
                    skipped_tests += summary.get("skipped", 0)
        
        execution_time = (
            (self.end_time - self.start_time).total_seconds()
            if self.start_time and self.end_time
            else 0
        )
        
        report = {
            "test_execution_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "skipped_tests": skipped_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                "execution_time": execution_time
            },
            "service_status": self.service_status,
            "test_results": self.test_results,
            "performance_metrics": self.performance_metrics
        }
        
        return report
    
    def save_test_report(self, report: Dict[str, Any], filename: str = "orchestration_test_report.json"):
        """Save test report to file."""
        try:
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            logger.info(f"Test report saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving test report: {e}")
    
    def print_test_summary(self, report: Dict[str, Any]):
        """Print test execution summary."""
        summary = report["test_execution_summary"]
        
        print("\n" + "="*60)
        print("ORCHESTRATION SYSTEM TEST RESULTS")
        print("="*60)
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed_tests']}")
        print(f"Failed: {summary['failed_tests']}")
        print(f"Skipped: {summary['skipped_tests']}")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        print(f"Execution Time: {summary['execution_time']:.2f} seconds")
        
        print("\nService Status:")
        for service, status in report["service_status"].items():
            status_text = "✓ Available" if status else "✗ Unavailable"
            print(f"  {service}: {status_text}")
        
        print("\nTest Categories:")
        for category, result in report["test_results"].items():
            if isinstance(result, dict) and "success" in result:
                status_text = "✓ PASSED" if result["success"] else "✗ FAILED"
                print(f"  {category}: {status_text}")
        
        print("="*60)
    
    async def run_all_tests(self):
        """Run all orchestration tests."""
        from datetime import datetime
        
        self.start_time = datetime.now()
        logger.info("Starting comprehensive orchestration system tests...")
        
        try:
            # Check service availability
            await self.check_service_availability()
            
            # Run test suites
            await self.run_plugin_manager_tests()
            await self.run_orchestration_integration_tests()
            await self.run_end_to_end_tests()
            await self.run_error_handling_tests()
            await self.run_performance_tests()
            
        except Exception as e:
            logger.error(f"Error during test execution: {e}")
        
        finally:
            self.end_time = datetime.now()
            
            # Generate and save report
            report = self.generate_test_report()
            self.save_test_report(report)
            self.print_test_summary(report)
            
            return report


async def main():
    """Main test execution function."""
    runner = OrchestrationTestRunner()
    
    try:
        report = await runner.run_all_tests()
        
        # Exit with appropriate code
        success_rate = report["test_execution_summary"]["success_rate"]
        if success_rate >= 80:
            logger.info("Test execution completed successfully")
            sys.exit(0)
        else:
            logger.error(f"Test execution completed with low success rate: {success_rate:.1f}%")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Test execution interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Change to the correct directory
    os.chdir(Path(__file__).parent.parent.parent)
    
    # Run tests
    asyncio.run(main())
