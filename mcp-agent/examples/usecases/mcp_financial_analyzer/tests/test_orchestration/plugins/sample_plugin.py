"""
Sample orchestration plugin for testing plugin manager functionality.

This plugin demonstrates a basic implementation of the OrchestrationPlugin
interface and is used for testing plugin registration, initialization,
and data processing capabilities.
"""

import logging
from typing import Dict, List, Any

from orchestrator.plugins import OrchestrationPlugin
from orchestrator.query_processor import QueryType

logger = logging.getLogger(__name__)


class SamplePlugin(OrchestrationPlugin):
    """Sample plugin for testing basic plugin functionality."""
    
    def __init__(self):
        super().__init__()
        self.processed_data_count = 0
        self.initialization_data = {}
    
    def get_name(self) -> str:
        return "sample_plugin"
    
    def get_version(self) -> str:
        return "1.0.0"
    
    def get_description(self) -> str:
        return "Sample plugin for testing orchestration plugin functionality"
    
    def get_author(self) -> str:
        return "MCP Test Suite"
    
    def get_supported_query_types(self) -> List[QueryType]:
        return [QueryType.SHORTAGE_ANALYSIS, QueryType.COMPREHENSIVE]
    
    def get_dependencies(self) -> List[str]:
        return []
    
    async def initialize(self, orchestrator: Any) -> bool:
        """Initialize the sample plugin."""
        try:
            logger.info("Initializing SamplePlugin")
            
            # Store orchestrator reference
            self.initialization_data = {
                "orchestrator_name": getattr(orchestrator, 'name', 'unknown'),
                "initialization_time": "test_time",
                "status": "initialized"
            }
            
            logger.info("SamplePlugin initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"SamplePlugin initialization failed: {e}")
            return False
    
    async def process_data(self, data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Process data with sample transformations."""
        try:
            logger.info(f"SamplePlugin processing data: {len(data)} items")
            
            # Increment processing counter
            self.processed_data_count += 1
            
            # Add plugin metadata to data
            processed_data = data.copy()
            processed_data.update({
                "sample_plugin_processed": True,
                "processing_count": self.processed_data_count,
                "plugin_name": self.get_name(),
                "plugin_version": self.get_version(),
                "context_keys": list(context.keys()) if context else []
            })
            
            # Simulate some data transformation
            if "shortage_data" in data:
                processed_data["sample_plugin_analysis"] = {
                    "data_quality": "good",
                    "processing_timestamp": "test_timestamp",
                    "recommendations": ["sample_recommendation_1", "sample_recommendation_2"]
                }
            
            logger.info("SamplePlugin data processing completed")
            return processed_data
            
        except Exception as e:
            logger.error(f"SamplePlugin data processing failed: {e}")
            # Return original data on error
            return data
    
    async def cleanup(self) -> None:
        """Cleanup sample plugin resources."""
        try:
            logger.info("Cleaning up SamplePlugin")
            
            # Reset counters and data
            self.processed_data_count = 0
            self.initialization_data.clear()
            
            logger.info("SamplePlugin cleanup completed")
            
        except Exception as e:
            logger.error(f"SamplePlugin cleanup failed: {e}")


class DataTransformPlugin(OrchestrationPlugin):
    """Plugin that performs data transformations between agents."""
    
    def __init__(self):
        super().__init__()
        self.transformation_rules = {}
    
    def get_name(self) -> str:
        return "data_transform_plugin"
    
    def get_version(self) -> str:
        return "1.0.0"
    
    def get_description(self) -> str:
        return "Plugin for transforming data between orchestration agents"
    
    def get_author(self) -> str:
        return "MCP Test Suite"
    
    def get_supported_query_types(self) -> List[QueryType]:
        return [QueryType.SHORTAGE_ANALYSIS, QueryType.COMPREHENSIVE, QueryType.INVENTORY_CHECK]
    
    def get_dependencies(self) -> List[str]:
        return []
    
    async def initialize(self, orchestrator: Any) -> bool:
        """Initialize data transformation rules."""
        try:
            logger.info("Initializing DataTransformPlugin")
            
            # Define transformation rules
            self.transformation_rules = {
                "mysql_to_shortage": {
                    "field_mappings": {
                        "inventory_data": "components",
                        "stock_levels": "available",
                        "demand_forecast": "required"
                    }
                },
                "shortage_to_alert": {
                    "field_mappings": {
                        "shortage_index": "severity_score",
                        "risk_level": "alert_level",
                        "component_analysis": "alert_details"
                    }
                }
            }
            
            logger.info("DataTransformPlugin initialized with transformation rules")
            return True
            
        except Exception as e:
            logger.error(f"DataTransformPlugin initialization failed: {e}")
            return False
    
    async def process_data(self, data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Transform data based on workflow context."""
        try:
            logger.info("DataTransformPlugin processing data transformation")
            
            # Determine transformation type from context
            current_agent = context.get("current_agent", "")
            next_agent = context.get("next_agent", "")
            transform_key = f"{current_agent}_to_{next_agent}"
            
            if transform_key in self.transformation_rules:
                rules = self.transformation_rules[transform_key]
                transformed_data = self._apply_transformation_rules(data, rules)
                
                logger.info(f"Applied transformation: {transform_key}")
                return transformed_data
            else:
                logger.info(f"No transformation rules for: {transform_key}")
                return data
                
        except Exception as e:
            logger.error(f"DataTransformPlugin processing failed: {e}")
            return data
    
    def _apply_transformation_rules(self, data: Dict[str, Any], rules: Dict[str, Any]) -> Dict[str, Any]:
        """Apply field mapping transformation rules."""
        transformed_data = data.copy()
        field_mappings = rules.get("field_mappings", {})
        
        for source_field, target_field in field_mappings.items():
            if source_field in data:
                transformed_data[target_field] = data[source_field]
                # Optionally remove source field
                # del transformed_data[source_field]
        
        # Add transformation metadata
        transformed_data["transformation_applied"] = True
        transformed_data["transformation_rules"] = rules
        
        return transformed_data
    
    async def cleanup(self) -> None:
        """Cleanup transformation plugin."""
        try:
            logger.info("Cleaning up DataTransformPlugin")
            self.transformation_rules.clear()
            logger.info("DataTransformPlugin cleanup completed")
            
        except Exception as e:
            logger.error(f"DataTransformPlugin cleanup failed: {e}")


class LoggingPlugin(OrchestrationPlugin):
    """Plugin that logs all orchestration events for debugging."""
    
    def __init__(self):
        super().__init__()
        self.event_log = []
    
    def get_name(self) -> str:
        return "logging_plugin"
    
    def get_version(self) -> str:
        return "1.0.0"
    
    def get_description(self) -> str:
        return "Plugin for logging orchestration events and data flow"
    
    def get_author(self) -> str:
        return "MCP Test Suite"
    
    def get_supported_query_types(self) -> List[QueryType]:
        return [QueryType.SHORTAGE_ANALYSIS, QueryType.COMPREHENSIVE, QueryType.INVENTORY_CHECK]  # Support multiple query types
    
    def get_dependencies(self) -> List[str]:
        return []
    
    async def initialize(self, orchestrator: Any) -> bool:
        """Initialize logging plugin."""
        try:
            logger.info("Initializing LoggingPlugin")
            self.event_log = []
            self._log_event("plugin_initialized", {"orchestrator": str(orchestrator)})
            # Log an additional event to ensure we have > 1 event for tests
            self._log_event("plugin_ready", {"status": "ready", "capabilities": ["logging", "event_tracking"]})
            return True
            
        except Exception as e:
            logger.error(f"LoggingPlugin initialization failed: {e}")
            return False
    
    async def process_data(self, data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Log data processing events."""
        try:
            # Log the processing event
            self._log_event("data_processed", {
                "data_keys": list(data.keys()),
                "context_keys": list(context.keys()) if context else [],
                "data_size": len(str(data))
            })
            
            # Add logging metadata to data
            processed_data = data.copy()
            processed_data["logging_plugin_events"] = len(self.event_log)
            
            return processed_data
            
        except Exception as e:
            logger.error(f"LoggingPlugin processing failed: {e}")
            return data
    
    def _log_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """Log an orchestration event."""
        event = {
            "timestamp": "test_timestamp",
            "event_type": event_type,
            "data": event_data
        }
        self.event_log.append(event)
        logger.info(f"LoggingPlugin event: {event_type}")
    
    def get_event_log(self) -> List[Dict[str, Any]]:
        """Get the complete event log."""
        return self.event_log.copy()
    
    async def cleanup(self) -> None:
        """Cleanup logging plugin."""
        try:
            logger.info("Cleaning up LoggingPlugin")
            self._log_event("plugin_cleanup", {"total_events": len(self.event_log)})
            # Keep event log for test verification
            logger.info("LoggingPlugin cleanup completed")
            
        except Exception as e:
            logger.error(f"LoggingPlugin cleanup failed: {e}")
