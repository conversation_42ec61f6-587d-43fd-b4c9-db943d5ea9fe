"""
Failing plugins for testing error handling and recovery scenarios.

These plugins intentionally fail at different stages to test the robustness
of the plugin manager and orchestration system error handling.
"""

import logging
from typing import Dict, List, Any

from orchestrator.plugins import OrchestrationPlugin
from orchestrator.query_processor import QueryType

logger = logging.getLogger(__name__)


class InitializationFailingPlugin(OrchestrationPlugin):
    """Plugin that fails during initialization."""
    
    def get_name(self) -> str:
        return "initialization_failing_plugin"
    
    def get_version(self) -> str:
        return "1.0.0"
    
    def get_description(self) -> str:
        return "Plugin that fails during initialization for testing error handling"
    
    def get_author(self) -> str:
        return "MCP Test Suite"
    
    def get_supported_query_types(self) -> List[QueryType]:
        return [QueryType.SHORTAGE_ANALYSIS]
    
    def get_dependencies(self) -> List[str]:
        return []
    
    async def initialize(self, orchestrator: Any) -> bool:
        """Intentionally fail during initialization."""
        logger.info("InitializationFailingPlugin attempting initialization")
        raise RuntimeError("Intentional initialization failure for testing")
    
    async def process_data(self, data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """This should never be called due to initialization failure."""
        return data
    
    async def cleanup(self) -> None:
        """Cleanup (should handle gracefully even if not initialized)."""
        logger.info("InitializationFailingPlugin cleanup")


class ProcessingFailingPlugin(OrchestrationPlugin):
    """Plugin that fails during data processing."""
    
    def __init__(self):
        super().__init__()
        self.initialized = False
    
    def get_name(self) -> str:
        return "processing_failing_plugin"
    
    def get_version(self) -> str:
        return "1.0.0"
    
    def get_description(self) -> str:
        return "Plugin that fails during data processing for testing error handling"
    
    def get_author(self) -> str:
        return "MCP Test Suite"
    
    def get_supported_query_types(self) -> List[QueryType]:
        return [QueryType.COMPREHENSIVE]
    
    def get_dependencies(self) -> List[str]:
        return []
    
    async def initialize(self, orchestrator: Any) -> bool:
        """Initialize successfully."""
        logger.info("ProcessingFailingPlugin initializing")
        self.initialized = True
        return True
    
    async def process_data(self, data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Intentionally fail during data processing."""
        logger.info("ProcessingFailingPlugin attempting data processing")
        raise ValueError("Intentional processing failure for testing")
    
    async def cleanup(self) -> None:
        """Cleanup successfully."""
        logger.info("ProcessingFailingPlugin cleanup")
        self.initialized = False


class CleanupFailingPlugin(OrchestrationPlugin):
    """Plugin that fails during cleanup."""
    
    def __init__(self):
        super().__init__()
        self.initialized = False
    
    def get_name(self) -> str:
        return "cleanup_failing_plugin"
    
    def get_version(self) -> str:
        return "1.0.0"
    
    def get_description(self) -> str:
        return "Plugin that fails during cleanup for testing error handling"
    
    def get_author(self) -> str:
        return "MCP Test Suite"
    
    def get_supported_query_types(self) -> List[QueryType]:
        return [QueryType.COMPREHENSIVE]
    
    def get_dependencies(self) -> List[str]:
        return []
    
    async def initialize(self, orchestrator: Any) -> bool:
        """Initialize successfully."""
        logger.info("CleanupFailingPlugin initializing")
        self.initialized = True
        return True
    
    async def process_data(self, data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Process data successfully."""
        logger.info("CleanupFailingPlugin processing data")
        processed_data = data.copy()
        processed_data["cleanup_failing_plugin_processed"] = True
        return processed_data
    
    async def cleanup(self) -> None:
        """Intentionally fail during cleanup."""
        logger.info("CleanupFailingPlugin attempting cleanup")
        raise RuntimeError("Intentional cleanup failure for testing")


class InvalidPlugin:
    """Invalid plugin that doesn't inherit from OrchestrationPlugin."""
    
    def get_name(self) -> str:
        return "invalid_plugin"
    
    def some_method(self) -> str:
        return "This is not a valid plugin"


class IncompletePlugin:
    """Plugin missing required methods - not inheriting from OrchestrationPlugin."""

    def get_name(self) -> str:
        return "incomplete_plugin"

    def get_version(self) -> str:
        return "1.0.0"

    # Missing other required methods intentionally


class DependentPlugin(OrchestrationPlugin):
    """Plugin with dependencies for testing dependency resolution."""
    
    def get_name(self) -> str:
        return "dependent_plugin"
    
    def get_version(self) -> str:
        return "1.0.0"
    
    def get_description(self) -> str:
        return "Plugin with dependencies for testing dependency resolution"
    
    def get_author(self) -> str:
        return "MCP Test Suite"
    
    def get_supported_query_types(self) -> List[QueryType]:
        return [QueryType.SHORTAGE_ANALYSIS]
    
    def get_dependencies(self) -> List[str]:
        return ["sample_plugin", "data_transform_plugin"]
    
    async def initialize(self, orchestrator: Any) -> bool:
        """Initialize with dependency checking."""
        logger.info("DependentPlugin initializing")
        
        # In a real implementation, would check if dependencies are available
        # For testing, we'll just succeed
        return True
    
    async def process_data(self, data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Process data with dependency information."""
        processed_data = data.copy()
        processed_data.update({
            "dependent_plugin_processed": True,
            "dependencies": self.get_dependencies()
        })
        return processed_data
    
    async def cleanup(self) -> None:
        """Cleanup dependent plugin."""
        logger.info("DependentPlugin cleanup")


class SlowPlugin(OrchestrationPlugin):
    """Plugin that takes a long time to process for timeout testing."""
    
    def get_name(self) -> str:
        return "slow_plugin"
    
    def get_version(self) -> str:
        return "1.0.0"
    
    def get_description(self) -> str:
        return "Plugin that processes slowly for timeout testing"
    
    def get_author(self) -> str:
        return "MCP Test Suite"
    
    def get_supported_query_types(self) -> List[QueryType]:
        return [QueryType.COMPREHENSIVE]
    
    def get_dependencies(self) -> List[str]:
        return []
    
    async def initialize(self, orchestrator: Any) -> bool:
        """Initialize normally."""
        logger.info("SlowPlugin initializing")
        return True
    
    async def process_data(self, data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Process data slowly."""
        import asyncio
        
        logger.info("SlowPlugin starting slow processing")
        
        # Simulate slow processing (but not too slow for tests)
        await asyncio.sleep(0.1)  # 100ms delay
        
        processed_data = data.copy()
        processed_data["slow_plugin_processed"] = True
        processed_data["processing_delay"] = "100ms"
        
        logger.info("SlowPlugin completed slow processing")
        return processed_data
    
    async def cleanup(self) -> None:
        """Cleanup normally."""
        logger.info("SlowPlugin cleanup")
