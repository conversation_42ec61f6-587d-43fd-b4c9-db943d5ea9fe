#!/usr/bin/env python3
"""
Comprehensive Service Health Checker for Orchestration Tests

This module provides automated health checks for all required services:
- Shortage Index Service (port 6970)
- Alert Notification Service (port 6971)  
- MySQL Database connectivity
- Test data validation

Implements detailed logging and retry mechanisms for robust service validation.
"""

import asyncio
import aiohttp
import logging
import time
import json
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import sys
import os

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ServiceHealthChecker:
    """Comprehensive service health checker for orchestration tests."""
    
    def __init__(self):
        self.services = {
            "shortage_service": {
                "name": "Shortage Index Service",
                "url": "http://localhost:6970",
                "endpoints": ["/", "/health", "/sse"],
                "required": True
            },
            "alert_service": {
                "name": "Alert Notification Service", 
                "url": "http://localhost:6971",
                "endpoints": ["/", "/health", "/sse"],
                "required": True
            }
        }
        self.health_results = {}
        self.detailed_results = {}
    
    async def check_service_endpoint(
        self, 
        service_name: str, 
        url: str, 
        endpoint: str,
        timeout: int = 10
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """Check a specific service endpoint."""
        full_url = f"{url}{endpoint}"
        
        try:
            async with aiohttp.ClientSession() as session:
                start_time = time.time()
                
                async with session.get(full_url, timeout=timeout) as response:
                    response_time = time.time() - start_time
                    
                    # Get response details
                    status = response.status
                    headers = dict(response.headers)
                    
                    try:
                        content = await response.text()
                        if response.headers.get('content-type', '').startswith('application/json'):
                            content = await response.json()
                    except Exception:
                        content = "Could not parse response content"
                    
                    # Determine if endpoint is healthy
                    is_healthy = status in [200, 404, 405]  # 404/405 means service is running
                    
                    details = {
                        "status_code": status,
                        "response_time": response_time,
                        "headers": headers,
                        "content_preview": str(content)[:200] if content else None
                    }
                    
                    message = f"Status {status}, Response time: {response_time:.3f}s"
                    
                    return is_healthy, message, details
                    
        except asyncio.TimeoutError:
            return False, f"Timeout after {timeout}s", {"error": "timeout"}
        except aiohttp.ClientConnectorError as e:
            return False, f"Connection failed: {e}", {"error": "connection_failed", "details": str(e)}
        except Exception as e:
            return False, f"Unexpected error: {e}", {"error": "unexpected", "details": str(e)}
    
    async def check_service_health(self, service_name: str) -> Dict[str, Any]:
        """Check health of a specific service."""
        service_config = self.services[service_name]
        service_url = service_config["url"]
        endpoints = service_config["endpoints"]
        
        logger.info(f"Checking health of {service_config['name']} at {service_url}")
        
        endpoint_results = {}
        overall_healthy = False
        
        for endpoint in endpoints:
            is_healthy, message, details = await self.check_service_endpoint(
                service_name, service_url, endpoint
            )
            
            endpoint_results[endpoint] = {
                "healthy": is_healthy,
                "message": message,
                "details": details
            }
            
            if is_healthy:
                overall_healthy = True
                logger.info(f"  {endpoint}: ✓ {message}")
            else:
                logger.warning(f"  {endpoint}: ✗ {message}")
        
        result = {
            "service_name": service_config["name"],
            "service_url": service_url,
            "overall_healthy": overall_healthy,
            "required": service_config["required"],
            "endpoints": endpoint_results,
            "check_timestamp": time.time()
        }
        
        return result
    
    async def check_mysql_connectivity(self) -> Dict[str, Any]:
        """Check MySQL database connectivity."""
        logger.info("Checking MySQL database connectivity")
        
        try:
            # Try to import and create MySQL agent
            from agents.mysql_agent import create_mysql_orchestrator_agent
            
            start_time = time.time()
            mysql_agent = create_mysql_orchestrator_agent()
            creation_time = time.time() - start_time
            
            # Test basic functionality if possible
            connectivity_test = True
            test_details = {
                "agent_creation_time": creation_time,
                "agent_type": type(mysql_agent).__name__,
                "has_llm_init": hasattr(mysql_agent, 'initialize_llm')
            }
            
            # Try to initialize LLM if available
            if hasattr(mysql_agent, 'initialize_llm'):
                try:
                    init_start = time.time()
                    await mysql_agent.initialize_llm()
                    init_time = time.time() - init_start
                    test_details["llm_init_time"] = init_time
                    test_details["llm_initialized"] = True
                except Exception as e:
                    test_details["llm_init_error"] = str(e)
                    test_details["llm_initialized"] = False
            
            logger.info(f"  MySQL agent created successfully in {creation_time:.3f}s")
            
            return {
                "service_name": "MySQL Database",
                "overall_healthy": connectivity_test,
                "required": True,
                "details": test_details,
                "check_timestamp": time.time()
            }
            
        except ImportError as e:
            logger.error(f"  MySQL agent import failed: {e}")
            return {
                "service_name": "MySQL Database",
                "overall_healthy": False,
                "required": True,
                "error": f"Import error: {e}",
                "check_timestamp": time.time()
            }
        except Exception as e:
            logger.error(f"  MySQL connectivity test failed: {e}")
            return {
                "service_name": "MySQL Database",
                "overall_healthy": False,
                "required": True,
                "error": f"Connectivity error: {e}",
                "check_timestamp": time.time()
            }
    
    async def validate_test_data_availability(self) -> Dict[str, Any]:
        """Validate test data files are available and properly formatted."""
        logger.info("Validating test data availability")
        
        test_data_dir = Path(__file__).parent / "test_data"
        required_files = [
            "financial_queries.json",
            "expected_results.json"
        ]
        
        file_results = {}
        overall_valid = True
        
        for filename in required_files:
            file_path = test_data_dir / filename
            
            try:
                if not file_path.exists():
                    file_results[filename] = {
                        "exists": False,
                        "valid": False,
                        "error": "File not found"
                    }
                    overall_valid = False
                    continue
                
                # Try to parse JSON
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                file_results[filename] = {
                    "exists": True,
                    "valid": True,
                    "size_bytes": file_path.stat().st_size,
                    "data_keys": list(data.keys()) if isinstance(data, dict) else None,
                    "data_count": len(data) if isinstance(data, (dict, list)) else None
                }
                
                logger.info(f"  {filename}: ✓ Valid JSON with {len(data)} items")
                
            except json.JSONDecodeError as e:
                file_results[filename] = {
                    "exists": True,
                    "valid": False,
                    "error": f"JSON parse error: {e}"
                }
                overall_valid = False
                logger.error(f"  {filename}: ✗ JSON parse error: {e}")
            except Exception as e:
                file_results[filename] = {
                    "exists": file_path.exists(),
                    "valid": False,
                    "error": f"Validation error: {e}"
                }
                overall_valid = False
                logger.error(f"  {filename}: ✗ Validation error: {e}")
        
        return {
            "service_name": "Test Data Files",
            "overall_healthy": overall_valid,
            "required": True,
            "files": file_results,
            "check_timestamp": time.time()
        }
    
    async def run_comprehensive_health_check(self) -> Dict[str, Any]:
        """Run comprehensive health check for all services."""
        logger.info("Starting comprehensive service health check")
        start_time = time.time()
        
        # Check all services
        for service_name in self.services.keys():
            result = await self.check_service_health(service_name)
            self.health_results[service_name] = result
        
        # Check MySQL connectivity
        mysql_result = await self.check_mysql_connectivity()
        self.health_results["mysql_database"] = mysql_result
        
        # Validate test data
        test_data_result = await self.validate_test_data_availability()
        self.health_results["test_data"] = test_data_result
        
        # Calculate overall health
        total_time = time.time() - start_time
        required_services = [
            result for result in self.health_results.values() 
            if result.get("required", False)
        ]
        healthy_required = [
            result for result in required_services 
            if result.get("overall_healthy", False)
        ]
        
        overall_health = {
            "all_services_healthy": len(healthy_required) == len(required_services),
            "healthy_services": len(healthy_required),
            "total_required_services": len(required_services),
            "health_check_duration": total_time,
            "timestamp": time.time()
        }
        
        # Detailed results
        self.detailed_results = {
            "overall_health": overall_health,
            "service_results": self.health_results
        }
        
        # Log summary
        if overall_health["all_services_healthy"]:
            logger.info(f"✓ All {len(required_services)} required services are healthy")
        else:
            unhealthy_count = len(required_services) - len(healthy_required)
            logger.error(f"✗ {unhealthy_count} of {len(required_services)} required services are unhealthy")
        
        logger.info(f"Health check completed in {total_time:.2f}s")
        
        return self.detailed_results
    
    def save_health_report(self, filename: str = "service_health_report.json"):
        """Save health check results to file."""
        try:
            with open(filename, 'w') as f:
                json.dump(self.detailed_results, f, indent=2, default=str)
            logger.info(f"Health report saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving health report: {e}")
    
    def print_health_summary(self):
        """Print a formatted health summary."""
        if not self.detailed_results:
            logger.error("No health check results available")
            return
        
        overall = self.detailed_results["overall_health"]
        services = self.detailed_results["service_results"]
        
        print("\n" + "="*60)
        print("SERVICE HEALTH CHECK SUMMARY")
        print("="*60)
        print(f"Overall Status: {'✓ HEALTHY' if overall['all_services_healthy'] else '✗ UNHEALTHY'}")
        print(f"Healthy Services: {overall['healthy_services']}/{overall['total_required_services']}")
        print(f"Check Duration: {overall['health_check_duration']:.2f}s")
        
        print("\nService Details:")
        for service_key, result in services.items():
            status = "✓ HEALTHY" if result.get("overall_healthy", False) else "✗ UNHEALTHY"
            name = result.get("service_name", service_key)
            print(f"  {name}: {status}")
            
            if "service_url" in result:
                print(f"    URL: {result['service_url']}")
            
            if "error" in result:
                print(f"    Error: {result['error']}")
        
        print("="*60)


async def main():
    """Main health check execution."""
    checker = ServiceHealthChecker()
    
    try:
        results = await checker.run_comprehensive_health_check()
        checker.save_health_report()
        checker.print_health_summary()
        
        # Exit with appropriate code
        if results["overall_health"]["all_services_healthy"]:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Health check interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
