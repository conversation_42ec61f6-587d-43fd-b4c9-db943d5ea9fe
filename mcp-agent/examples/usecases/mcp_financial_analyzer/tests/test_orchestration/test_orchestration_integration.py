"""
Integration tests for orchestration system components.

These tests validate the integration between:
- OrchestrationRunner and PluginManager
- FinancialOrchestrator and plugin system
- Context management with plugin data flow
- Query processing with plugin filtering
- Workflow execution with plugin processing
"""

import pytest
import pytest_asyncio
import asyncio
import logging
from unittest.mock import Mock, patch

from orchestrator.plugins import PluginManager, PluginStatus
from orchestrator.orchestration_runner import OrchestrationRunner
from orchestrator.financial_orchestrator import FinancialOrchestrator
from orchestrator.context_manager import ContextManager
from orchestrator.query_processor import FinancialQueryProcessor, QueryType
from orchestrator.workflow_patterns import WorkflowPatternRegistry, WorkflowExecutor

logger = logging.getLogger(__name__)


@pytest.mark.orchestration
@pytest.mark.integration
class TestOrchestrationRunnerIntegration:
    """Test integration between OrchestrationRunner and plugin system."""
    
    @pytest.mark.asyncio
    async def test_orchestration_runner_initialization(self, orchestration_runner):
        """Test orchestration runner initializes with all components."""
        assert orchestration_runner is not None
        assert orchestration_runner.mysql_agent is not None
        assert orchestration_runner.shortage_agent is not None
        assert orchestration_runner.alert_agent is not None
        assert orchestration_runner.context_manager is not None
        assert orchestration_runner.query_processor is not None
        assert orchestration_runner.pattern_registry is not None
        assert orchestration_runner.workflow_executor is not None
        assert orchestration_runner.orchestrator is not None
    
    @pytest.mark.asyncio
    async def test_orchestration_runner_with_plugins(
        self, orchestration_runner, plugin_manager_with_plugins
    ):
        """Test orchestration runner integration with plugin system."""
        # Integrate plugin manager with orchestration runner
        orchestration_runner.plugin_manager = plugin_manager_with_plugins
        
        # Initialize plugins with orchestrator
        results = await plugin_manager_with_plugins.initialize_all_plugins(
            orchestration_runner.orchestrator
        )
        
        # Verify plugin initialization
        assert len(results) == 3  # sample, data_transform, logging plugins
        assert all(success for success in results.values())
        
        # Verify plugins are available for query processing
        shortage_plugins = plugin_manager_with_plugins.get_plugins_for_query_type(
            QueryType.SHORTAGE_ANALYSIS
        )
        assert len(shortage_plugins) >= 2  # sample and data_transform plugins
    
    @pytest.mark.asyncio
    async def test_orchestration_runner_query_execution_with_plugins(
        self, orchestration_runner, plugin_manager_with_plugins, financial_test_queries
    ):
        """Test query execution with plugin processing."""
        # Integrate plugin manager
        orchestration_runner.plugin_manager = plugin_manager_with_plugins
        
        # Initialize plugins
        await plugin_manager_with_plugins.initialize_all_plugins(
            orchestration_runner.orchestrator
        )
        
        # Execute a test query
        test_query = financial_test_queries["shortage_analysis"]["query"]
        
        # Mock the actual agent execution to focus on orchestration
        with patch.object(orchestration_runner, '_execute_pattern_based') as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "mysql_results": {"inventory_data": "test_data"},
                "shortage_results": {"shortage_index": 0.75},
                "plugin_processing": "completed"
            }
            
            result = await orchestration_runner.execute_financial_query(test_query)
            
            # Verify query execution
            assert result["success"] is True
            assert "workflow_id" in result
            assert "execution_time" in result
            assert "query_analysis" in result
            
            # Verify mock was called
            mock_execute.assert_called_once()


@pytest.mark.orchestration
@pytest.mark.integration
class TestFinancialOrchestratorIntegration:
    """Test integration between FinancialOrchestrator and plugin system."""
    
    @pytest.mark.asyncio
    async def test_financial_orchestrator_with_plugins(
        self, financial_orchestrator, plugin_manager_with_plugins
    ):
        """Test financial orchestrator integration with plugins."""
        # Integrate plugin manager
        financial_orchestrator.plugin_manager = plugin_manager_with_plugins
        
        # Initialize plugins
        results = await plugin_manager_with_plugins.initialize_all_plugins(
            financial_orchestrator
        )
        
        # Verify plugin initialization
        assert len(results) == 3
        assert all(success for success in results.values())
        
        # Verify orchestrator can access plugins
        all_plugins = plugin_manager_with_plugins.get_all_plugins()
        assert len(all_plugins) == 3
        
        for plugin_name, plugin in all_plugins.items():
            assert plugin.status == PluginStatus.INITIALIZED
    
    @pytest.mark.asyncio
    async def test_financial_orchestrator_plugin_data_flow(
        self, financial_orchestrator, plugin_manager_with_plugins, orchestration_test_data
    ):
        """Test data flow through plugins in financial orchestrator."""
        # Integrate and initialize plugins
        financial_orchestrator.plugin_manager = plugin_manager_with_plugins
        await plugin_manager_with_plugins.initialize_all_plugins(financial_orchestrator)
        
        # Get plugins for testing
        logging_plugin = plugin_manager_with_plugins.get_plugin("logging_plugin")
        data_transform_plugin = plugin_manager_with_plugins.get_plugin("data_transform_plugin")
        
        assert logging_plugin is not None
        assert data_transform_plugin is not None
        
        # Test data processing through plugins
        test_data = orchestration_test_data["mysql_sample_data"]
        test_context = {
            "workflow_id": "test_workflow",
            "current_agent": "mysql",
            "next_agent": "shortage"
        }
        
        # Process through logging plugin first
        logged_data = await logging_plugin.process_data(test_data, test_context)
        assert logged_data["logging_plugin_events"] > 0
        
        # Process through data transform plugin
        transformed_data = await data_transform_plugin.process_data(logged_data, test_context)
        assert transformed_data["transformation_applied"] is True
        
        # Verify data flow preservation
        assert "inventory_data" in transformed_data  # Original data preserved
        assert "logging_plugin_events" in transformed_data  # Previous plugin data preserved


@pytest.mark.orchestration
@pytest.mark.integration
class TestContextManagerPluginIntegration:
    """Test integration between context manager and plugin system."""
    
    def test_context_manager_with_plugin_data(self, context_manager, orchestration_test_data):
        """Test context manager handling plugin-processed data."""
        workflow_id = "test_workflow_001"
        
        # Create context
        context = context_manager.create_context(
            workflow_id=workflow_id,
            query="Test query with plugins",
            query_type="SHORTAGE_ANALYSIS",
            workflow_pattern="SEQUENTIAL"
        )
        
        assert context is not None
        assert context.workflow_id == workflow_id
        
        # Add plugin-processed data to context
        plugin_data = {
            "sample_plugin_processed": True,
            "transformation_applied": True,
            "logging_plugin_events": 5,
            "original_data": orchestration_test_data["mysql_sample_data"]
        }
        
        context_manager.update_agent_context(
            workflow_id, "mysql_analyzer", plugin_data
        )
        
        # Retrieve and verify context
        mysql_context = context_manager.get_agent_context(workflow_id, "mysql_analyzer")
        assert mysql_context is not None
        assert mysql_context["sample_plugin_processed"] is True
        assert mysql_context["transformation_applied"] is True
        assert mysql_context["logging_plugin_events"] == 5
    
    def test_context_sharing_between_plugins(
        self, context_manager, plugin_manager_with_plugins, orchestration_test_data
    ):
        """Test context sharing between different plugins."""
        workflow_id = "test_workflow_002"
        
        # Create context
        context = context_manager.create_context(
            workflow_id=workflow_id,
            query="Test plugin context sharing",
            query_type="COMPREHENSIVE_ANALYSIS",
            workflow_pattern="SEQUENTIAL"
        )
        
        # Simulate plugin processing with context updates
        test_data = orchestration_test_data["shortage_sample_data"]
        
        # First plugin processes data and updates context
        plugin1_result = test_data.copy()
        plugin1_result["plugin1_processed"] = True
        plugin1_result["processing_stage"] = "initial"
        
        context_manager.update_agent_context(
            workflow_id, "plugin1", plugin1_result
        )
        
        # Second plugin accesses previous context and processes
        plugin1_context = context_manager.get_agent_context(workflow_id, "plugin1")
        
        plugin2_result = plugin1_context.copy()
        plugin2_result["plugin2_processed"] = True
        plugin2_result["processing_stage"] = "enhanced"
        plugin2_result["previous_stage"] = plugin1_context["processing_stage"]
        
        context_manager.update_agent_context(
            workflow_id, "plugin2", plugin2_result
        )
        
        # Verify context sharing
        final_context = context_manager.get_agent_context(workflow_id, "plugin2")
        assert final_context["plugin1_processed"] is True
        assert final_context["plugin2_processed"] is True
        assert final_context["previous_stage"] == "initial"
        assert final_context["processing_stage"] == "enhanced"


@pytest.mark.orchestration
@pytest.mark.integration
class TestQueryProcessorPluginIntegration:
    """Test integration between query processor and plugin system."""
    
    def test_query_processor_with_plugin_filtering(
        self, query_processor, plugin_manager_with_plugins, financial_test_queries
    ):
        """Test query processor filtering plugins by query type."""
        # Process different types of queries
        for query_name, query_info in financial_test_queries.items():
            query_text = query_info["query"]
            expected_type = query_info["expected_type"]
            
            # Process query
            parsed_query = query_processor.process_query(query_text)
            
            # Get plugins that support this query type
            supporting_plugins = plugin_manager_with_plugins.get_plugins_for_query_type(
                parsed_query.query_type
            )
            
            # Verify plugin filtering
            assert len(supporting_plugins) > 0
            
            # Verify plugins actually support the query type
            for plugin in supporting_plugins:
                supported_types = plugin.get_supported_query_types()
                assert parsed_query.query_type in supported_types
    
    def test_query_complexity_with_plugin_requirements(
        self, query_processor, plugin_manager_with_plugins, financial_test_queries
    ):
        """Test query complexity assessment with plugin requirements."""
        # Test comprehensive analysis query (should require multiple plugins)
        comprehensive_query = financial_test_queries["comprehensive_analysis"]["query"]
        parsed_query = query_processor.process_query(comprehensive_query)
        
        # Should be high complexity
        assert parsed_query.complexity_score > 0.7
        
        # Should have multiple supporting plugins
        supporting_plugins = plugin_manager_with_plugins.get_plugins_for_query_type(
            parsed_query.query_type
        )
        assert len(supporting_plugins) >= 2  # data_transform and logging plugins


@pytest.mark.orchestration
@pytest.mark.integration
class TestWorkflowExecutorPluginIntegration:
    """Test integration between workflow executor and plugin system."""
    
    @pytest.mark.asyncio
    async def test_workflow_executor_with_plugin_processing(
        self, workflow_executor, plugin_manager_with_plugins, orchestration_test_data
    ):
        """Test workflow execution with plugin data processing."""
        # Initialize plugins
        mock_orchestrator = Mock()
        await plugin_manager_with_plugins.initialize_all_plugins(mock_orchestrator)
        
        # Get a workflow pattern
        pattern = workflow_executor.pattern_registry.get_pattern("sequential_analysis")
        assert pattern is not None
        
        # Mock agents for workflow execution
        mock_agents = {
            "mysql_analyzer": Mock(),
            "shortage_analyzer": Mock(),
            "alert_manager": Mock()
        }
        
        # Mock context manager
        mock_context_manager = Mock()
        
        # Test data with plugin processing markers
        input_data = orchestration_test_data["mysql_sample_data"].copy()
        input_data["plugins_available"] = True
        input_data["plugin_count"] = len(plugin_manager_with_plugins.get_all_plugins())
        
        # Mock workflow execution (focus on plugin integration)
        with patch.object(workflow_executor, '_execute_step') as mock_execute_step:
            mock_execute_step.return_value = {
                "success": True,
                "data": input_data,
                "plugin_processed": True
            }
            
            # Execute workflow pattern
            workflow_id = "test_workflow_003"
            results = await workflow_executor.execute_pattern(
                pattern_id=pattern.pattern_id,
                workflow_id=workflow_id,
                agents=mock_agents,
                context_manager=mock_context_manager,
                input_data=input_data
            )
            
            # Verify workflow execution
            assert results["success"] is True
            assert results["workflow_id"] == workflow_id
            assert results["pattern_id"] == pattern.pattern_id
            
            # Verify steps were executed
            assert mock_execute_step.call_count > 0


@pytest.mark.orchestration
@pytest.mark.integration
@pytest.mark.performance
class TestOrchestrationPerformanceWithPlugins:
    """Test orchestration system performance with plugin processing."""
    
    @pytest.mark.asyncio
    async def test_orchestration_performance_with_multiple_plugins(
        self, orchestration_runner, plugin_manager_with_plugins, 
        orchestration_performance_monitor, financial_test_queries
    ):
        """Test orchestration performance with multiple plugins."""
        # Integrate plugin manager
        orchestration_runner.plugin_manager = plugin_manager_with_plugins
        
        # Initialize plugins
        await plugin_manager_with_plugins.initialize_all_plugins(
            orchestration_runner.orchestrator
        )
        
        # Monitor performance
        orchestration_performance_monitor.start_monitoring()
        
        # Execute multiple queries
        test_queries = [
            financial_test_queries["shortage_analysis"]["query"],
            financial_test_queries["alert_generation"]["query"],
            financial_test_queries["cpu_shortage"]["query"]
        ]
        
        # Mock execution to focus on orchestration overhead
        with patch.object(orchestration_runner, '_execute_pattern_based') as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "plugin_processing_time": 0.05,
                "total_plugins_used": 3
            }
            
            results = []
            for query in test_queries:
                result = await orchestration_runner.execute_financial_query(query)
                results.append(result)
        
        orchestration_performance_monitor.stop_monitoring()
        metrics = orchestration_performance_monitor.get_metrics()
        
        # Verify all queries executed successfully
        assert len(results) == 3
        assert all(result["success"] for result in results)
        
        # Verify reasonable performance
        assert metrics["elapsed_time"] < 2.0  # Should complete within 2 seconds
        
        # Verify plugin integration didn't cause excessive overhead
        avg_execution_time = sum(
            result["execution_time"] for result in results
        ) / len(results)
        assert avg_execution_time < 0.5  # Average execution under 500ms
    
    @pytest.mark.asyncio
    async def test_plugin_cleanup_performance(
        self, plugin_manager_with_plugins, orchestration_performance_monitor
    ):
        """Test plugin cleanup performance."""
        # Initialize plugins first
        mock_orchestrator = Mock()
        await plugin_manager_with_plugins.initialize_all_plugins(mock_orchestrator)
        
        # Monitor cleanup performance
        orchestration_performance_monitor.start_monitoring()
        
        await plugin_manager_with_plugins.cleanup_all_plugins()
        
        orchestration_performance_monitor.stop_monitoring()
        metrics = orchestration_performance_monitor.get_metrics()
        
        # Verify cleanup completed quickly
        assert metrics["elapsed_time"] < 1.0  # Should cleanup within 1 second
        
        # Verify all plugins are cleaned up
        for plugin in plugin_manager_with_plugins.get_all_plugins().values():
            # Plugins should be in unregistered state after cleanup
            assert plugin.status in [PluginStatus.UNREGISTERED, PluginStatus.CLEANUP]
