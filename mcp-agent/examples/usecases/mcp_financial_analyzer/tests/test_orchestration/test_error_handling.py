"""
Error handling and recovery tests for the orchestration system.

These tests validate:
- Service unavailability scenarios and graceful degradation
- Plugin failure recovery and error propagation
- Network timeout handling and retry mechanisms
- Invalid input validation and error responses
- Workflow interruption and recovery scenarios
"""

import pytest
import pytest_asyncio
import asyncio
import logging
from unittest.mock import Mock, patch, AsyncMock
from aiohttp import ClientConnectorError, ClientTimeout

from orchestrator.plugins import PluginStatus
from orchestrator.exceptions import OrchestrationError
from tests.test_orchestration.plugins.failing_plugin import (
    InitializationFailingPlugin, ProcessingFailingPlugin, CleanupFailingPlugin
)

logger = logging.getLogger(__name__)


@pytest.mark.orchestration
@pytest.mark.error_handling
class TestServiceUnavailabilityHandling:
    """Test handling of service unavailability scenarios."""
    
    @pytest.mark.asyncio
    async def test_shortage_service_unavailable(self, orchestration_runner):
        """Test workflow behavior when shortage service is unavailable."""
        # Mock shortage service unavailability
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.side_effect = ClientConnectorError(
                connection_key=Mock(), os_error=OSError("Connection refused")
            )
            
            query = "Analyze inventory shortages for Q4 2024"
            
            try:
                result = await orchestration_runner.execute_financial_query(
                    query=query,
                    execution_mode="pattern_based"
                )
                
                # Should handle gracefully - either succeed with degraded functionality
                # or fail with clear error message
                if result["success"]:
                    # If successful, should indicate service limitations
                    assert "service_warnings" in result or "degraded_mode" in result
                else:
                    # If failed, should have clear error information
                    assert "error" in result or "service_unavailable" in result
                
                logger.info("Shortage service unavailability handled gracefully")
                
            except Exception as e:
                # Should not raise unhandled exceptions
                logger.error(f"Unhandled exception during service unavailability: {e}")
                pytest.fail(f"Service unavailability not handled gracefully: {e}")
    
    @pytest.mark.asyncio
    async def test_alert_service_unavailable(self, orchestration_runner):
        """Test workflow behavior when alert service is unavailable."""
        # Mock alert service unavailability
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.side_effect = ClientConnectorError(
                connection_key=Mock(), os_error=OSError("Connection refused")
            )
            
            query = "Generate alerts for low stock items"
            
            try:
                result = await orchestration_runner.execute_financial_query(
                    query=query,
                    execution_mode="pattern_based"
                )
                
                # Alert service unavailability should be handled gracefully
                # Workflow might succeed with warnings or fail with clear error
                assert isinstance(result, dict)
                assert "workflow_id" in result
                
                if not result.get("success", False):
                    # Should have informative error message
                    assert any(key in result for key in ["error", "service_error", "alert_error"])
                
                logger.info("Alert service unavailability handled gracefully")
                
            except Exception as e:
                logger.error(f"Unhandled exception during alert service unavailability: {e}")
                pytest.fail(f"Alert service unavailability not handled gracefully: {e}")
    
    @pytest.mark.asyncio
    async def test_multiple_services_unavailable(self, orchestration_runner):
        """Test workflow behavior when multiple services are unavailable."""
        # Mock multiple service unavailability
        with patch('aiohttp.ClientSession.post') as mock_post, \
             patch('aiohttp.ClientSession.get') as mock_get:
            
            mock_post.side_effect = ClientConnectorError(
                connection_key=Mock(), os_error=OSError("Connection refused")
            )
            mock_get.side_effect = ClientConnectorError(
                connection_key=Mock(), os_error=OSError("Connection refused")
            )
            
            query = "Comprehensive supply chain analysis with alerts"
            
            try:
                result = await orchestration_runner.execute_financial_query(
                    query=query,
                    execution_mode="pattern_based"
                )
                
                # Should handle multiple service failures gracefully
                assert isinstance(result, dict)
                assert "workflow_id" in result
                
                # Should indicate service issues
                if not result.get("success", False):
                    assert any(key in result for key in [
                        "error", "service_errors", "multiple_service_failures"
                    ])
                
                logger.info("Multiple service unavailability handled gracefully")
                
            except Exception as e:
                logger.error(f"Unhandled exception during multiple service unavailability: {e}")
                pytest.fail(f"Multiple service unavailability not handled gracefully: {e}")


@pytest.mark.orchestration
@pytest.mark.error_handling
class TestPluginErrorHandling:
    """Test plugin error handling and recovery scenarios."""
    
    @pytest.mark.asyncio
    async def test_plugin_initialization_failure_recovery(self, plugin_manager):
        """Test recovery from plugin initialization failures."""
        # Register failing plugin
        failing_plugin = InitializationFailingPlugin()
        success = plugin_manager.register_plugin(failing_plugin)
        assert success is True
        
        # Also register a working plugin
        from tests.test_orchestration.plugins.sample_plugin import SamplePlugin
        working_plugin = SamplePlugin()
        success = plugin_manager.register_plugin(working_plugin)
        assert success is True
        
        # Initialize plugins (one should fail, one should succeed)
        mock_orchestrator = Mock()
        results = await plugin_manager.initialize_all_plugins(mock_orchestrator)
        
        # Verify mixed results
        assert len(results) == 2
        assert results[failing_plugin.get_name()] is False
        assert results[working_plugin.get_name()] is True
        
        # Verify plugin states
        assert failing_plugin.status == PluginStatus.FAILED
        assert working_plugin.status == PluginStatus.INITIALIZED
        
        # Verify error information
        assert failing_plugin.error_message is not None
        assert "Intentional initialization failure" in failing_plugin.error_message
        
        logger.info("Plugin initialization failure recovery validated")
    
    @pytest.mark.asyncio
    async def test_plugin_processing_failure_recovery(self, plugin_manager):
        """Test recovery from plugin processing failures."""
        # Register processing failing plugin
        failing_plugin = ProcessingFailingPlugin()
        plugin_manager.register_plugin(failing_plugin)
        
        # Initialize plugin (should succeed)
        mock_orchestrator = Mock()
        results = await plugin_manager.initialize_all_plugins(mock_orchestrator)
        assert results[failing_plugin.get_name()] is True
        
        # Test data processing (should fail)
        test_data = {"test_field": "test_value"}
        test_context = {"workflow_id": "test_workflow"}
        
        with pytest.raises(ValueError, match="Intentional processing failure"):
            await failing_plugin.process_data(test_data, test_context)
        
        # Plugin should still be in initialized state
        assert failing_plugin.status == PluginStatus.INITIALIZED
        
        logger.info("Plugin processing failure recovery validated")
    
    @pytest.mark.asyncio
    async def test_plugin_cleanup_failure_recovery(self, plugin_manager):
        """Test recovery from plugin cleanup failures."""
        # Register cleanup failing plugin
        failing_plugin = CleanupFailingPlugin()
        plugin_manager.register_plugin(failing_plugin)
        
        # Initialize plugin
        mock_orchestrator = Mock()
        results = await plugin_manager.initialize_all_plugins(mock_orchestrator)
        assert results[failing_plugin.get_name()] is True
        
        # Cleanup plugins (should handle failure gracefully)
        await plugin_manager.cleanup_all_plugins()
        
        # Plugin should have error message but cleanup should have completed
        assert failing_plugin.error_message is not None
        assert "Intentional cleanup failure" in failing_plugin.error_message
        
        logger.info("Plugin cleanup failure recovery validated")
    
    @pytest.mark.asyncio
    async def test_plugin_manager_resilience_with_mixed_failures(self, plugin_manager):
        """Test plugin manager resilience with multiple types of failures."""
        # Register multiple failing plugins
        init_failing = InitializationFailingPlugin()
        process_failing = ProcessingFailingPlugin()
        cleanup_failing = CleanupFailingPlugin()
        
        plugin_manager.register_plugin(init_failing)
        plugin_manager.register_plugin(process_failing)
        plugin_manager.register_plugin(cleanup_failing)
        
        # Initialize plugins (mixed results expected)
        mock_orchestrator = Mock()
        results = await plugin_manager.initialize_all_plugins(mock_orchestrator)
        
        # Verify initialization results
        assert results[init_failing.get_name()] is False  # Should fail
        assert results[process_failing.get_name()] is True  # Should succeed
        assert results[cleanup_failing.get_name()] is True  # Should succeed
        
        # Test processing on successfully initialized plugins
        test_data = {"test_field": "test_value"}
        test_context = {}
        
        # Process failing plugin should raise exception
        with pytest.raises(ValueError):
            await process_failing.process_data(test_data, test_context)
        
        # Cleanup failing plugin should process successfully
        result = await cleanup_failing.process_data(test_data, test_context)
        assert result["cleanup_failing_plugin_processed"] is True
        
        # Cleanup all plugins (should handle mixed failures)
        await plugin_manager.cleanup_all_plugins()
        
        logger.info("Plugin manager resilience with mixed failures validated")


@pytest.mark.orchestration
@pytest.mark.error_handling
class TestNetworkTimeoutHandling:
    """Test network timeout handling and retry mechanisms."""
    
    @pytest.mark.asyncio
    async def test_service_timeout_handling(self, orchestration_runner):
        """Test handling of service timeouts."""
        # Mock service timeout
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.side_effect = asyncio.TimeoutError("Request timeout")
            
            query = "Analyze inventory shortages with timeout simulation"
            
            try:
                result = await orchestration_runner.execute_financial_query(
                    query=query,
                    execution_mode="pattern_based"
                )
                
                # Should handle timeout gracefully
                assert isinstance(result, dict)
                assert "workflow_id" in result
                
                # Should indicate timeout or service issues
                if not result.get("success", False):
                    assert any(key in result for key in [
                        "error", "timeout_error", "service_timeout"
                    ])
                
                logger.info("Service timeout handled gracefully")
                
            except Exception as e:
                logger.error(f"Unhandled exception during timeout: {e}")
                pytest.fail(f"Service timeout not handled gracefully: {e}")
    
    @pytest.mark.asyncio
    async def test_slow_service_response_handling(self, orchestration_runner):
        """Test handling of slow service responses."""
        # Mock slow service response
        async def slow_response(*args, **kwargs):
            await asyncio.sleep(0.5)  # 500ms delay
            mock_response = Mock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value={"result": "slow_response"})
            return mock_response
        
        with patch('aiohttp.ClientSession.post', side_effect=slow_response):
            query = "Test slow service response handling"
            
            try:
                result = await orchestration_runner.execute_financial_query(
                    query=query,
                    execution_mode="pattern_based"
                )
                
                # Should complete successfully despite slow response
                assert isinstance(result, dict)
                assert "workflow_id" in result
                
                # Execution time should reflect the delay
                if "execution_time" in result:
                    assert result["execution_time"] >= 0.5
                
                logger.info("Slow service response handled successfully")
                
            except Exception as e:
                logger.error(f"Error handling slow service response: {e}")
                raise


@pytest.mark.orchestration
@pytest.mark.error_handling
class TestInvalidInputHandling:
    """Test handling of invalid inputs and malformed queries."""
    
    @pytest.mark.asyncio
    async def test_empty_query_handling(self, orchestration_runner):
        """Test handling of empty or whitespace-only queries."""
        empty_queries = ["", "   ", "\n\t  ", None]
        
        for query in empty_queries:
            try:
                result = await orchestration_runner.execute_financial_query(
                    query=query,
                    execution_mode="pattern_based"
                )
                
                # Should handle empty query gracefully
                assert isinstance(result, dict)
                
                # Should indicate input validation error
                if not result.get("success", False):
                    assert any(key in result for key in [
                        "error", "validation_error", "invalid_query"
                    ])
                
            except Exception as e:
                logger.error(f"Unhandled exception for empty query '{query}': {e}")
                pytest.fail(f"Empty query not handled gracefully: {e}")
        
        logger.info("Empty query handling validated")
    
    @pytest.mark.asyncio
    async def test_malformed_query_handling(self, orchestration_runner):
        """Test handling of malformed or nonsensical queries."""
        malformed_queries = [
            "!@#$%^&*()",
            "SELECT * FROM nonexistent_table;",
            "This is not a financial query at all",
            "123456789",
            "query with\x00null\x00bytes"
        ]
        
        for query in malformed_queries:
            try:
                result = await orchestration_runner.execute_financial_query(
                    query=query,
                    execution_mode="pattern_based"
                )
                
                # Should handle malformed query gracefully
                assert isinstance(result, dict)
                assert "workflow_id" in result
                
                # May succeed with low confidence or fail with validation error
                if not result.get("success", False):
                    assert any(key in result for key in [
                        "error", "validation_error", "parsing_error"
                    ])
                else:
                    # If successful, should have low confidence
                    query_analysis = result.get("query_analysis", {})
                    confidence = query_analysis.get("confidence", 1.0)
                    assert confidence < 0.5  # Low confidence for malformed queries
                
            except Exception as e:
                logger.error(f"Unhandled exception for malformed query '{query}': {e}")
                pytest.fail(f"Malformed query not handled gracefully: {e}")
        
        logger.info("Malformed query handling validated")
    
    @pytest.mark.asyncio
    async def test_ambiguous_query_handling(self, orchestration_runner):
        """Test handling of ambiguous queries requiring clarification."""
        ambiguous_queries = [
            "Analyze something",
            "Generate report",
            "Check status",
            "What about the things?",
            "Fix the problem"
        ]
        
        for query in ambiguous_queries:
            try:
                result = await orchestration_runner.execute_financial_query(
                    query=query,
                    execution_mode="pattern_based"
                )
                
                # Should handle ambiguous query gracefully
                assert isinstance(result, dict)
                assert "workflow_id" in result
                
                # Should either succeed with low confidence or request clarification
                if result.get("requires_clarification", False):
                    assert "ambiguity_flags" in result
                    assert "suggested_clarifications" in result
                else:
                    # If processed, should have low confidence
                    query_analysis = result.get("query_analysis", {})
                    confidence = query_analysis.get("confidence", 1.0)
                    assert confidence < 0.7  # Low confidence for ambiguous queries
                
            except Exception as e:
                logger.error(f"Unhandled exception for ambiguous query '{query}': {e}")
                pytest.fail(f"Ambiguous query not handled gracefully: {e}")
        
        logger.info("Ambiguous query handling validated")


@pytest.mark.orchestration
@pytest.mark.error_handling
class TestWorkflowInterruptionRecovery:
    """Test workflow interruption and recovery scenarios."""
    
    @pytest.mark.asyncio
    async def test_workflow_cancellation_handling(self, orchestration_runner):
        """Test handling of workflow cancellation."""
        query = "Long running comprehensive analysis"
        
        # Start workflow execution
        workflow_task = asyncio.create_task(
            orchestration_runner.execute_financial_query(
                query=query,
                execution_mode="pattern_based"
            )
        )
        
        # Cancel after short delay
        await asyncio.sleep(0.1)
        workflow_task.cancel()
        
        try:
            result = await workflow_task
            # Should not reach here if properly cancelled
            pytest.fail("Workflow should have been cancelled")
            
        except asyncio.CancelledError:
            # Expected cancellation
            logger.info("Workflow cancellation handled correctly")
            
        except Exception as e:
            logger.error(f"Unexpected exception during cancellation: {e}")
            raise
    
    @pytest.mark.asyncio
    async def test_partial_workflow_failure_recovery(self, orchestration_runner):
        """Test recovery from partial workflow failures."""
        # Mock partial failure scenario
        with patch.object(orchestration_runner, '_execute_pattern_based') as mock_execute:
            # Simulate partial failure
            mock_execute.return_value = {
                "success": False,
                "partial_results": {
                    "mysql_results": {"status": "completed"},
                    "shortage_results": {"status": "failed", "error": "Service unavailable"},
                    "alert_results": {"status": "not_executed"}
                },
                "error": "Shortage analysis failed",
                "recovery_suggestions": ["Retry shortage analysis", "Use cached data"]
            }
            
            query = "Comprehensive analysis with partial failure"
            
            result = await orchestration_runner.execute_financial_query(
                query=query,
                execution_mode="pattern_based"
            )
            
            # Should handle partial failure gracefully
            assert isinstance(result, dict)
            assert "workflow_id" in result
            assert result["success"] is False
            
            # Should provide recovery information
            assert "partial_results" in result
            assert "recovery_suggestions" in result
            
            logger.info("Partial workflow failure recovery validated")
    
    @pytest.mark.asyncio
    async def test_context_corruption_recovery(self, context_manager):
        """Test recovery from context corruption scenarios."""
        workflow_id = "test_workflow_corruption"
        
        # Create context
        context = context_manager.create_context(
            workflow_id=workflow_id,
            query="Test context corruption recovery",
            query_type="SHORTAGE_ANALYSIS",
            workflow_pattern="SEQUENTIAL"
        )
        
        assert context is not None
        
        # Simulate context corruption
        try:
            # Add invalid data to context
            context_manager.update_agent_context(
                workflow_id, "mysql_analyzer", {"invalid": float('inf')}
            )
            
            # Try to retrieve context
            retrieved_context = context_manager.get_agent_context(workflow_id, "mysql_analyzer")
            
            # Should handle corruption gracefully
            assert retrieved_context is not None
            
        except Exception as e:
            # Should not raise unhandled exceptions
            logger.error(f"Context corruption not handled gracefully: {e}")
            pytest.fail(f"Context corruption recovery failed: {e}")
        
        logger.info("Context corruption recovery validated")
