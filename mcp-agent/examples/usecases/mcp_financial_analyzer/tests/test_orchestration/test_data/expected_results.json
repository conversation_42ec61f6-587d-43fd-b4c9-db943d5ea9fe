{"shortage_analysis_results": {"required_fields": ["shortage_index", "weighted_shortage_index", "risk_level", "component_analysis"], "optional_fields": ["recommendations", "historical_trends", "forecast_data"], "mysql_data_fields": ["inventory_data", "historical_data", "demand_forecast"], "shortage_calculation_fields": ["shortage_ratios", "priority_scores", "risk_assessment"], "expected_components": ["cpu", "gpu", "motherboard", "fans"], "risk_levels": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}, "alert_management_results": {"required_fields": ["alerts_sent", "notification_results", "alert_summary"], "optional_fields": ["delivery_confirmations", "failed_deliveries", "retry_attempts"], "alert_channels": ["HTTP", "MQTT", "EMAIL"], "delivery_status_values": ["SENT", "DELIVERED", "FAILED", "PENDING", "RETRY"], "severity_levels": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]}, "comprehensive_analysis_results": {"required_fields": ["mysql_results", "shortage_results", "alert_results", "workflow_summary", "execution_time"], "workflow_metadata": ["workflow_id", "start_time", "end_time", "execution_mode", "query_analysis"], "context_fields": ["agent_contexts", "data_flow_summary", "processing_stages"]}, "plugin_processing_results": {"sample_plugin_fields": ["sample_plugin_processed", "processing_count", "plugin_name", "plugin_version"], "data_transform_plugin_fields": ["transformation_applied", "transformation_rules", "field_mappings"], "logging_plugin_fields": ["logging_plugin_events", "event_log_size", "processing_timestamps"]}, "error_response_structure": {"required_fields": ["success", "workflow_id", "error"], "optional_fields": ["error_code", "error_details", "recovery_suggestions", "partial_results"], "service_error_fields": ["service_name", "service_status", "error_message", "retry_count"]}, "performance_benchmarks": {"shortage_analysis": {"max_execution_time": 20.0, "typical_execution_time": 5.0, "max_memory_usage_mb": 50.0}, "alert_management": {"max_execution_time": 15.0, "typical_execution_time": 3.0, "max_memory_usage_mb": 30.0}, "comprehensive_analysis": {"max_execution_time": 45.0, "typical_execution_time": 15.0, "max_memory_usage_mb": 100.0}, "plugin_processing": {"max_overhead_ms": 100.0, "typical_overhead_ms": 20.0, "max_memory_overhead_mb": 10.0}}, "data_validation_rules": {"shortage_index": {"type": "float", "min_value": 0.0, "max_value": 1.0}, "weighted_shortage_index": {"type": "float", "min_value": 0.0, "max_value": 1.0}, "component_shortage_ratio": {"type": "float", "min_value": 0.0, "max_value": 10.0}, "execution_time": {"type": "float", "min_value": 0.0, "max_value": 300.0}, "confidence_score": {"type": "float", "min_value": 0.0, "max_value": 1.0}}, "workflow_patterns": {"sequential_analysis": {"steps": ["mysql_data_retrieval", "shortage_calculation", "alert_generation"], "execution_mode": "SEQUENTIAL"}, "parallel_analysis": {"steps": [["mysql_historical_analysis", "shortage_current_analysis"], "alert_consolidation"], "execution_mode": "PARALLEL"}, "hybrid_analysis": {"steps": ["mysql_data_retrieval", ["shortage_calculation", "risk_assessment"], "alert_generation"], "execution_mode": "HYBRID"}}, "test_data_samples": {"basic_shortage_scenario": {"components": {"cpu": {"available": 1, "required": 2, "expected_shortage_ratio": 0.5}, "gpu": {"available": 2, "required": 6, "expected_shortage_ratio": 0.67}, "motherboard": {"available": 1, "required": 3, "expected_shortage_ratio": 0.67}, "fans": {"available": 1, "required": 6, "expected_shortage_ratio": 0.83}}, "expected_overall_shortage": 0.625, "expected_risk_level": "HIGH"}, "weighted_shortage_scenario": {"components": {"cpu": {"available": 1, "required": 2, "weight": 0.2, "expected_contribution": 0.1}, "gpu": {"available": 2, "required": 6, "weight": 0.6, "expected_contribution": 0.4}, "motherboard": {"available": 1, "required": 3, "weight": 0.1, "expected_contribution": 0.067}, "fans": {"available": 1, "required": 6, "weight": 0.1, "expected_contribution": 0.083}}, "expected_weighted_shortage": 0.82, "expected_risk_level": "CRITICAL"}}}