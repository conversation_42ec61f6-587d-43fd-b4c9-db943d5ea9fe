{"shortage_analysis_queries": [{"query": "Analyze inventory shortages for Q4 2024", "expected_type": "SHORTAGE_ANALYSIS", "expected_agents": ["mysql_analyzer", "shortage_analyzer"], "complexity": "medium", "confidence_threshold": 0.7}, {"query": "What are the current CPU shortage levels?", "expected_type": "SHORTAGE_ANALYSIS", "expected_agents": ["mysql_analyzer", "shortage_analyzer"], "complexity": "low", "confidence_threshold": 0.8, "entities": ["CPU"]}, {"query": "Calculate weighted shortage index for all components", "expected_type": "SHORTAGE_ANALYSIS", "expected_agents": ["mysql_analyzer", "shortage_analyzer"], "complexity": "high", "confidence_threshold": 0.6}], "alert_management_queries": [{"query": "Generate alerts for low stock items", "expected_type": "ALERT_MANAGEMENT", "expected_agents": ["shortage_analyzer", "alert_manager"], "complexity": "medium", "confidence_threshold": 0.7}, {"query": "Send critical shortage notifications", "expected_type": "ALERT_MANAGEMENT", "expected_agents": ["alert_manager"], "complexity": "low", "confidence_threshold": 0.8}, {"query": "Notify stakeholders about GPU shortage via email and HTTP", "expected_type": "ALERT_MANAGEMENT", "expected_agents": ["alert_manager"], "complexity": "medium", "confidence_threshold": 0.7, "entities": ["GPU"], "channels": ["EMAIL", "HTTP"]}], "comprehensive_analysis_queries": [{"query": "Comprehensive supply chain analysis with alerts", "expected_type": "COMPREHENSIVE_ANALYSIS", "expected_agents": ["mysql_analyzer", "shortage_analyzer", "alert_manager"], "complexity": "high", "confidence_threshold": 0.6}, {"query": "Analyze inventory data, calculate shortages, and send alerts", "expected_type": "COMPREHENSIVE_ANALYSIS", "expected_agents": ["mysql_analyzer", "shortage_analyzer", "alert_manager"], "complexity": "high", "confidence_threshold": 0.7}, {"query": "Full financial analysis with shortage assessment and notification system", "expected_type": "COMPREHENSIVE_ANALYSIS", "expected_agents": ["mysql_analyzer", "shortage_analyzer", "alert_manager"], "complexity": "high", "confidence_threshold": 0.5}], "ambiguous_queries": [{"query": "Analyze something", "expected_clarification": true, "ambiguity_flags": ["vague_subject", "missing_context"], "confidence_threshold": 0.3}, {"query": "Generate report", "expected_clarification": true, "ambiguity_flags": ["unspecified_report_type", "missing_parameters"], "confidence_threshold": 0.4}, {"query": "Check status", "expected_clarification": true, "ambiguity_flags": ["unspecified_entity", "vague_action"], "confidence_threshold": 0.2}], "invalid_queries": [{"query": "", "expected_error": "empty_query", "should_fail": true}, {"query": "!@#$%^&*()", "expected_error": "invalid_characters", "should_fail": false, "confidence_threshold": 0.1}, {"query": "SELECT * FROM inventory WHERE shortage > 0;", "expected_error": "sql_injection_attempt", "should_fail": false, "confidence_threshold": 0.2}], "performance_test_queries": [{"query": "Quick CPU shortage check", "expected_type": "SHORTAGE_ANALYSIS", "max_execution_time": 10.0, "complexity": "low"}, {"query": "Comprehensive multi-component analysis with detailed reporting", "expected_type": "COMPREHENSIVE_ANALYSIS", "max_execution_time": 45.0, "complexity": "high"}, {"query": "Batch alert generation for all critical shortages", "expected_type": "ALERT_MANAGEMENT", "max_execution_time": 20.0, "complexity": "medium"}], "plugin_test_queries": [{"query": "Analyze inventory with enhanced data transformation", "expected_type": "SHORTAGE_ANALYSIS", "required_plugins": ["data_transform_plugin"], "plugin_processing_expected": true}, {"query": "Generate detailed logs for shortage analysis workflow", "expected_type": "SHORTAGE_ANALYSIS", "required_plugins": ["logging_plugin"], "plugin_processing_expected": true}, {"query": "Transform and analyze inventory data with full logging", "expected_type": "SHORTAGE_ANALYSIS", "required_plugins": ["data_transform_plugin", "logging_plugin"], "plugin_processing_expected": true}]}