"""
Integration tests for the PluginManager and OrchestrationPlugin system.

These tests validate:
- Plugin registration and unregistration
- Plugin discovery and loading from directories
- Plugin lifecycle management (initialize, process, cleanup)
- Error handling for plugin failures
- Inter-plugin communication and data flow
"""

import pytest
import pytest_asyncio
import asyncio
import logging
from pathlib import Path
from unittest.mock import Mock

from orchestrator.plugins import PluginManager, OrchestrationPlugin, PluginStatus
from orchestrator.query_processor import QueryType
from tests.test_orchestration.plugins.sample_plugin import SamplePlugin, DataTransformPlugin, LoggingPlugin
from tests.test_orchestration.plugins.failing_plugin import (
    InitializationFailingPlugin, ProcessingFailingPlugin, CleanupFailingPlugin,
    DependentPlugin, SlowPlugin, InvalidPlugin, IncompletePlugin
)

logger = logging.getLogger(__name__)


@pytest.mark.plugin_test
class TestPluginManager:
    """Test suite for PluginManager functionality."""
    
    def test_plugin_manager_initialization(self, plugin_manager):
        """Test plugin manager initializes correctly."""
        assert plugin_manager is not None
        assert len(plugin_manager.get_all_plugins()) == 0
        assert len(plugin_manager.get_all_plugin_info()) == 0
    
    def test_plugin_registration_success(self, plugin_manager, sample_plugins):
        """Test successful plugin registration."""
        sample_plugin = sample_plugins["sample"]
        
        # Register plugin
        success = plugin_manager.register_plugin(sample_plugin)
        assert success is True
        
        # Verify registration
        assert sample_plugin.get_name() in plugin_manager.get_all_plugins()
        assert sample_plugin.status == PluginStatus.REGISTERED
        
        # Verify plugin info
        plugin_info = plugin_manager.get_plugin_info(sample_plugin.get_name())
        assert plugin_info is not None
        assert plugin_info.name == sample_plugin.get_name()
        assert plugin_info.version == sample_plugin.get_version()
        assert plugin_info.status == PluginStatus.REGISTERED
    
    def test_plugin_registration_duplicate(self, plugin_manager, sample_plugins):
        """Test duplicate plugin registration handling."""
        sample_plugin = sample_plugins["sample"]
        
        # Register plugin twice
        success1 = plugin_manager.register_plugin(sample_plugin)
        success2 = plugin_manager.register_plugin(sample_plugin)
        
        assert success1 is True
        assert success2 is True  # Should replace existing
        
        # Should still have only one plugin
        all_plugins = plugin_manager.get_all_plugins()
        assert len(all_plugins) == 1
        assert sample_plugin.get_name() in all_plugins
    
    def test_plugin_unregistration(self, plugin_manager, sample_plugins):
        """Test plugin unregistration."""
        sample_plugin = sample_plugins["sample"]
        plugin_name = sample_plugin.get_name()
        
        # Register then unregister
        plugin_manager.register_plugin(sample_plugin)
        assert plugin_name in plugin_manager.get_all_plugins()
        
        success = plugin_manager.unregister_plugin(plugin_name)
        assert success is True
        assert plugin_name not in plugin_manager.get_all_plugins()
    
    def test_plugin_unregistration_nonexistent(self, plugin_manager):
        """Test unregistering non-existent plugin."""
        success = plugin_manager.unregister_plugin("nonexistent_plugin")
        assert success is False
    
    def test_get_plugin_by_name(self, plugin_manager, sample_plugins):
        """Test retrieving plugin by name."""
        sample_plugin = sample_plugins["sample"]
        plugin_name = sample_plugin.get_name()
        
        # Plugin not registered yet
        assert plugin_manager.get_plugin(plugin_name) is None
        
        # Register and retrieve
        plugin_manager.register_plugin(sample_plugin)
        retrieved_plugin = plugin_manager.get_plugin(plugin_name)
        
        assert retrieved_plugin is not None
        assert retrieved_plugin == sample_plugin
        assert retrieved_plugin.get_name() == plugin_name
    
    def test_get_plugins_for_query_type(self, plugin_manager, sample_plugins):
        """Test filtering plugins by query type."""
        # Register plugins with different query type support
        sample_plugin = sample_plugins["sample"]  # Supports SHORTAGE_ANALYSIS, ALERT_MANAGEMENT
        data_transform_plugin = sample_plugins["data_transform"]  # Supports all types
        
        plugin_manager.register_plugin(sample_plugin)
        plugin_manager.register_plugin(data_transform_plugin)
        
        # Test filtering by SHORTAGE_ANALYSIS
        shortage_plugins = plugin_manager.get_plugins_for_query_type(QueryType.SHORTAGE_ANALYSIS)
        assert len(shortage_plugins) == 2
        assert sample_plugin in shortage_plugins
        assert data_transform_plugin in shortage_plugins
        
        # Test filtering by COMPREHENSIVE
        comprehensive_plugins = plugin_manager.get_plugins_for_query_type(QueryType.COMPREHENSIVE)
        assert len(comprehensive_plugins) == 2
        assert sample_plugin in comprehensive_plugins
        assert data_transform_plugin in comprehensive_plugins

        # Test filtering by INVENTORY_CHECK
        inventory_plugins = plugin_manager.get_plugins_for_query_type(QueryType.INVENTORY_CHECK)
        assert len(inventory_plugins) == 1
        assert data_transform_plugin in inventory_plugins
    
    @pytest.mark.asyncio
    async def test_plugin_initialization_success(self, plugin_manager, sample_plugins):
        """Test successful plugin initialization."""
        sample_plugin = sample_plugins["sample"]
        plugin_manager.register_plugin(sample_plugin)
        
        # Create mock orchestrator
        mock_orchestrator = Mock()
        mock_orchestrator.name = "test_orchestrator"
        
        # Initialize plugins
        results = await plugin_manager.initialize_all_plugins(mock_orchestrator)
        
        assert len(results) == 1
        assert results[sample_plugin.get_name()] is True
        assert sample_plugin.status == PluginStatus.INITIALIZED
        
        # Verify initialization data
        assert sample_plugin.initialization_data["orchestrator_name"] == "test_orchestrator"
        assert sample_plugin.initialization_data["status"] == "initialized"
    
    @pytest.mark.asyncio
    async def test_plugin_initialization_failure(self, plugin_manager, failing_plugins):
        """Test plugin initialization failure handling."""
        failing_plugin = failing_plugins["init_fail"]
        plugin_manager.register_plugin(failing_plugin)
        
        mock_orchestrator = Mock()
        
        # Initialize plugins (should handle failure gracefully)
        results = await plugin_manager.initialize_all_plugins(mock_orchestrator)
        
        assert len(results) == 1
        assert results[failing_plugin.get_name()] is False
        assert failing_plugin.status == PluginStatus.FAILED
        assert failing_plugin.error_message is not None
    
    @pytest.mark.asyncio
    async def test_plugin_cleanup(self, plugin_manager, sample_plugins):
        """Test plugin cleanup."""
        sample_plugin = sample_plugins["sample"]
        plugin_manager.register_plugin(sample_plugin)
        
        # Initialize first
        mock_orchestrator = Mock()
        await plugin_manager.initialize_all_plugins(mock_orchestrator)
        assert sample_plugin.status == PluginStatus.INITIALIZED
        
        # Cleanup
        await plugin_manager.cleanup_all_plugins()
        
        # Verify cleanup (plugin should reset its state)
        assert sample_plugin.processed_data_count == 0
        assert len(sample_plugin.initialization_data) == 0
    
    @pytest.mark.asyncio
    async def test_plugin_cleanup_failure(self, plugin_manager, failing_plugins):
        """Test plugin cleanup failure handling."""
        cleanup_failing_plugin = failing_plugins["cleanup_fail"]
        plugin_manager.register_plugin(cleanup_failing_plugin)
        
        # Initialize first
        mock_orchestrator = Mock()
        await plugin_manager.initialize_all_plugins(mock_orchestrator)
        assert cleanup_failing_plugin.status == PluginStatus.INITIALIZED
        
        # Cleanup (should handle failure gracefully)
        await plugin_manager.cleanup_all_plugins()
        
        # Plugin should have error but cleanup should complete
        assert cleanup_failing_plugin.error_message is not None
    
    def test_plugin_loading_from_directory(self, plugin_manager, temp_plugin_directory):
        """Test loading plugins from directory."""
        # Load plugins from temporary directory
        loaded_plugins = plugin_manager.load_plugins_from_directory(temp_plugin_directory)
        
        # Should have loaded multiple plugins
        assert len(loaded_plugins) > 0
        
        # Verify plugins are registered
        all_plugins = plugin_manager.get_all_plugins()
        assert len(all_plugins) >= len(loaded_plugins)
        
        # Check for expected plugin names
        plugin_names = [plugin.get_name() for plugin in loaded_plugins]
        expected_names = ["sample_plugin", "data_transform_plugin", "logging_plugin"]
        
        for expected_name in expected_names:
            assert any(expected_name in name for name in plugin_names)
    
    def test_plugin_loading_from_nonexistent_directory(self, plugin_manager):
        """Test loading plugins from non-existent directory."""
        nonexistent_dir = Path("/nonexistent/directory")
        loaded_plugins = plugin_manager.load_plugins_from_directory(nonexistent_dir)
        
        assert len(loaded_plugins) == 0
        assert len(plugin_manager.get_all_plugins()) == 0
    
    def test_plugin_validation_success(self, plugin_manager, sample_plugins):
        """Test plugin validation for valid plugins."""
        sample_plugin = sample_plugins["sample"]
        
        # Validation should succeed during registration
        success = plugin_manager.register_plugin(sample_plugin)
        assert success is True
    
    def test_plugin_validation_failure_invalid_plugin(self, plugin_manager):
        """Test plugin validation failure for invalid plugin."""
        invalid_plugin = InvalidPlugin()
        
        # Should fail validation (not an OrchestrationPlugin)
        success = plugin_manager.register_plugin(invalid_plugin)
        assert success is False
    
    def test_plugin_validation_failure_incomplete_plugin(self, plugin_manager):
        """Test plugin validation failure for incomplete plugin."""
        incomplete_plugin = IncompletePlugin()
        
        # Should fail validation (missing required methods)
        success = plugin_manager.register_plugin(incomplete_plugin)
        assert success is False


@pytest.mark.plugin_test
class TestPluginDataProcessing:
    """Test suite for plugin data processing functionality."""
    
    @pytest.mark.asyncio
    async def test_sample_plugin_data_processing(self, sample_plugins):
        """Test sample plugin data processing."""
        sample_plugin = sample_plugins["sample"]
        
        # Initialize plugin
        mock_orchestrator = Mock()
        await sample_plugin._safe_initialize(mock_orchestrator)
        
        # Test data processing
        test_data = {
            "shortage_data": {
                "cpu": {"available": 1, "required": 2},
                "gpu": {"available": 2, "required": 6}
            },
            "original_field": "test_value"
        }
        
        test_context = {
            "workflow_id": "test_workflow",
            "current_agent": "test_agent"
        }
        
        # Process data
        result = await sample_plugin.process_data(test_data, test_context)
        
        # Verify processing
        assert result["sample_plugin_processed"] is True
        assert result["processing_count"] == 1
        assert result["plugin_name"] == "sample_plugin"
        assert result["plugin_version"] == "1.0.0"
        assert "sample_plugin_analysis" in result
        assert result["original_field"] == "test_value"  # Original data preserved
        
        # Process again to test counter
        result2 = await sample_plugin.process_data(test_data, test_context)
        assert result2["processing_count"] == 2
    
    @pytest.mark.asyncio
    async def test_data_transform_plugin_processing(self, sample_plugins):
        """Test data transformation plugin."""
        transform_plugin = sample_plugins["data_transform"]
        
        # Initialize plugin
        mock_orchestrator = Mock()
        await transform_plugin._safe_initialize(mock_orchestrator)
        
        # Test data transformation
        test_data = {
            "inventory_data": {"cpu": 1, "gpu": 2},
            "stock_levels": {"cpu": "low", "gpu": "adequate"},
            "demand_forecast": {"cpu": 2, "gpu": 6}
        }
        
        test_context = {
            "current_agent": "mysql",
            "next_agent": "shortage"
        }
        
        # Process data
        result = await transform_plugin.process_data(test_data, test_context)
        
        # Verify transformation
        assert result["transformation_applied"] is True
        assert "components" in result  # Mapped from inventory_data
        assert "available" in result   # Mapped from stock_levels
        assert "required" in result    # Mapped from demand_forecast
        assert "transformation_rules" in result
    
    @pytest.mark.asyncio
    async def test_logging_plugin_processing(self, sample_plugins):
        """Test logging plugin functionality."""
        logging_plugin = sample_plugins["logging"]
        
        # Initialize plugin
        mock_orchestrator = Mock()
        await logging_plugin._safe_initialize(mock_orchestrator)
        
        # Verify initialization logged
        event_log = logging_plugin.get_event_log()
        assert len(event_log) == 1
        assert event_log[0]["event_type"] == "plugin_initialized"
        
        # Test data processing
        test_data = {"test_field": "test_value"}
        test_context = {"workflow_id": "test_workflow"}
        
        result = await logging_plugin.process_data(test_data, test_context)
        
        # Verify processing logged
        event_log = logging_plugin.get_event_log()
        assert len(event_log) == 2
        assert event_log[1]["event_type"] == "data_processed"
        
        # Verify result includes logging metadata
        assert result["logging_plugin_events"] == 2
        assert result["test_field"] == "test_value"  # Original data preserved
    
    @pytest.mark.asyncio
    async def test_plugin_processing_failure_handling(self, failing_plugins):
        """Test plugin data processing failure handling."""
        processing_failing_plugin = failing_plugins["process_fail"]
        
        # Initialize plugin
        mock_orchestrator = Mock()
        await processing_failing_plugin._safe_initialize(mock_orchestrator)
        assert processing_failing_plugin.status == PluginStatus.INITIALIZED
        
        # Test data processing (should fail gracefully)
        test_data = {"test_field": "test_value"}
        test_context = {}
        
        # Processing should raise exception but plugin should handle it
        with pytest.raises(ValueError, match="Intentional processing failure"):
            await processing_failing_plugin.process_data(test_data, test_context)


@pytest.mark.plugin_test
class TestPluginDependencies:
    """Test suite for plugin dependency management."""
    
    def test_plugin_dependencies_declaration(self, failing_plugins):
        """Test plugin dependency declaration."""
        dependent_plugin = failing_plugins["dependent"]
        
        dependencies = dependent_plugin.get_dependencies()
        assert len(dependencies) == 2
        assert "sample_plugin" in dependencies
        assert "data_transform_plugin" in dependencies
    
    @pytest.mark.asyncio
    async def test_dependent_plugin_processing(self, failing_plugins):
        """Test dependent plugin data processing."""
        dependent_plugin = failing_plugins["dependent"]
        
        # Initialize plugin
        mock_orchestrator = Mock()
        await dependent_plugin._safe_initialize(mock_orchestrator)
        
        # Test data processing
        test_data = {"test_field": "test_value"}
        test_context = {}
        
        result = await dependent_plugin.process_data(test_data, test_context)
        
        # Verify dependency information included
        assert result["dependent_plugin_processed"] is True
        assert result["dependencies"] == ["sample_plugin", "data_transform_plugin"]
        assert result["test_field"] == "test_value"  # Original data preserved


@pytest.mark.plugin_test
@pytest.mark.performance
class TestPluginPerformance:
    """Test suite for plugin performance characteristics."""
    
    @pytest.mark.asyncio
    async def test_slow_plugin_processing(self, failing_plugins, orchestration_performance_monitor):
        """Test slow plugin processing and timeout handling."""
        slow_plugin = failing_plugins["slow"]
        
        # Initialize plugin
        mock_orchestrator = Mock()
        await slow_plugin._safe_initialize(mock_orchestrator)
        
        # Monitor performance
        orchestration_performance_monitor.start_monitoring()
        
        # Process data (should take some time)
        test_data = {"test_field": "test_value"}
        test_context = {}
        
        result = await slow_plugin.process_data(test_data, test_context)
        
        orchestration_performance_monitor.stop_monitoring()
        metrics = orchestration_performance_monitor.get_metrics()
        
        # Verify processing completed
        assert result["slow_plugin_processed"] is True
        assert result["processing_delay"] == "100ms"
        
        # Verify timing (should be at least 100ms)
        assert metrics["elapsed_time"] >= 0.1
    
    @pytest.mark.asyncio
    async def test_multiple_plugin_initialization_performance(
        self, plugin_manager, sample_plugins, orchestration_performance_monitor
    ):
        """Test performance of initializing multiple plugins."""
        # Register multiple plugins
        for plugin in sample_plugins.values():
            plugin_manager.register_plugin(plugin)
        
        mock_orchestrator = Mock()
        
        # Monitor initialization performance
        orchestration_performance_monitor.start_monitoring()
        
        results = await plugin_manager.initialize_all_plugins(mock_orchestrator)
        
        orchestration_performance_monitor.stop_monitoring()
        metrics = orchestration_performance_monitor.get_metrics()
        
        # Verify all plugins initialized successfully
        assert len(results) == len(sample_plugins)
        assert all(success for success in results.values())
        
        # Verify reasonable performance (should complete quickly)
        assert metrics["elapsed_time"] < 1.0  # Less than 1 second
