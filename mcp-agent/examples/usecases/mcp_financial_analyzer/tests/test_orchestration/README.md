# Orchestration System Integration and End-to-End Tests

This directory contains comprehensive tests for the MCP Financial Analyzer orchestration system, including plugin architecture, integration testing, and end-to-end workflow validation.

## Test Structure

```
test_orchestration/
├── __init__.py                           # Package initialization
├── conftest.py                          # Orchestration-specific fixtures
├── test_plugin_manager.py               # Plugin system integration tests
├── test_orchestration_integration.py    # Component integration tests
├── test_end_to_end_workflows.py        # Complete workflow tests
├── test_error_handling.py              # Error handling and recovery tests
├── run_orchestration_tests.py          # Comprehensive test runner
├── plugins/                             # Sample plugins for testing
│   ├── __init__.py
│   ├── sample_plugin.py                # Working plugins for testing
│   └── failing_plugin.py               # Failing plugins for error testing
├── test_data/                          # Test data and expected results
│   ├── financial_queries.json         # Test queries and scenarios
│   └── expected_results.json          # Expected result structures
└── README.md                           # This file
```

## Test Categories

### 1. Plugin Manager Tests (`test_plugin_manager.py`)
- **Plugin Registration**: Test plugin registration, unregistration, and validation
- **Plugin Discovery**: Test loading plugins from directories
- **Plugin Lifecycle**: Test initialization, processing, and cleanup
- **Error Handling**: Test plugin failure scenarios and recovery
- **Data Processing**: Test plugin data transformation capabilities
- **Performance**: Test plugin processing performance and resource usage

### 2. Orchestration Integration Tests (`test_orchestration_integration.py`)
- **Component Integration**: Test integration between orchestration components
- **Plugin System Integration**: Test orchestration with plugin processing
- **Context Management**: Test context sharing with plugin data
- **Query Processing**: Test query processor with plugin filtering
- **Workflow Execution**: Test workflow executor with plugin processing
- **Performance**: Test orchestration performance with plugins

### 3. End-to-End Workflow Tests (`test_end_to_end_workflows.py`)
- **Complete Workflows**: Test realistic financial analysis scenarios
- **Data Flow Validation**: Test data flow through all three agents
- **Real Service Integration**: Test with actual MCP services
- **Plugin Integration**: Test workflows with plugin processing
- **Performance Monitoring**: Test workflow performance characteristics

### 4. Error Handling Tests (`test_error_handling.py`)
- **Service Unavailability**: Test graceful degradation when services are down
- **Plugin Failures**: Test recovery from plugin initialization/processing failures
- **Network Timeouts**: Test timeout handling and retry mechanisms
- **Invalid Inputs**: Test validation and error responses for bad inputs
- **Workflow Interruption**: Test cancellation and recovery scenarios

## Prerequisites

### Required Services
The tests require the following services to be running:

1. **Shortage Index Service** (port 6970)
   ```bash
   # Start the shortage-index service
   cd /merge/agent_develop/index
   python -m uvicorn main:app --host 0.0.0.0 --port 6970
   ```

2. **Alert Notification Service** (port 6971)
   ```bash
   # Start the alert-notification service
   cd /merge/agent_develop/notification
   python -m uvicorn main:app --host 0.0.0.0 --port 6971
   ```

3. **MySQL Database** (configured in environment)
   - Ensure MySQL connection is properly configured
   - Database should contain test financial data

### Python Dependencies
```bash
pip install pytest pytest-asyncio aiohttp psutil
```

### Optional Dependencies for Enhanced Testing
```bash
pip install pytest-json-report pytest-html pytest-cov
```

## Running the Tests

### Option 1: Comprehensive Test Runner (Recommended)
```bash
cd /merge/mcp-agent/examples/usecases/mcp_financial_analyzer/tests/test_orchestration
python run_orchestration_tests.py
```

This will:
- Check service availability
- Run all test categories in sequence
- Generate comprehensive test reports
- Provide performance metrics
- Save results to JSON and log files

### Option 2: Individual Test Categories
```bash
cd /merge/mcp-agent/examples/usecases/mcp_financial_analyzer

# Plugin manager tests
pytest tests/test_orchestration/test_plugin_manager.py -v -m plugin_test

# Integration tests
pytest tests/test_orchestration/test_orchestration_integration.py -v -m integration

# End-to-end tests (requires services)
pytest tests/test_orchestration/test_end_to_end_workflows.py -v -m "end_to_end and real_service"

# Error handling tests
pytest tests/test_orchestration/test_error_handling.py -v -m error_handling

# Performance tests only
pytest tests/test_orchestration/ -v -m performance
```

### Option 3: Specific Test Scenarios
```bash
# Test specific workflow scenarios
pytest tests/test_orchestration/test_end_to_end_workflows.py::TestEndToEndWorkflows::test_shortage_analysis_workflow -v

# Test plugin system only
pytest tests/test_orchestration/test_plugin_manager.py -v

# Test error handling scenarios
pytest tests/test_orchestration/test_error_handling.py::TestServiceUnavailabilityHandling -v
```

## Test Markers

The tests use pytest markers for categorization:

- `@pytest.mark.orchestration` - General orchestration tests
- `@pytest.mark.plugin_test` - Plugin system tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.end_to_end` - End-to-end workflow tests
- `@pytest.mark.real_service` - Tests requiring real services
- `@pytest.mark.error_handling` - Error handling tests
- `@pytest.mark.performance` - Performance tests

## Test Data

### Financial Queries (`test_data/financial_queries.json`)
Contains realistic financial analysis queries categorized by:
- Shortage analysis queries
- Alert management queries
- Comprehensive analysis queries
- Ambiguous queries (for error testing)
- Invalid queries (for validation testing)
- Performance test queries
- Plugin-specific test queries

### Expected Results (`test_data/expected_results.json`)
Defines expected result structures for:
- Shortage analysis results
- Alert management results
- Comprehensive analysis results
- Plugin processing results
- Error response structures
- Performance benchmarks
- Data validation rules

## Sample Plugins

### Working Plugins (`plugins/sample_plugin.py`)
- **SamplePlugin**: Basic plugin functionality demonstration
- **DataTransformPlugin**: Data transformation between agents
- **LoggingPlugin**: Event logging and debugging

### Failing Plugins (`plugins/failing_plugin.py`)
- **InitializationFailingPlugin**: Fails during initialization
- **ProcessingFailingPlugin**: Fails during data processing
- **CleanupFailingPlugin**: Fails during cleanup
- **DependentPlugin**: Has dependencies for testing
- **SlowPlugin**: Slow processing for timeout testing

## Test Results and Reporting

### Generated Files
- `orchestration_test_results.log` - Detailed test execution log
- `orchestration_test_report.json` - Comprehensive test results in JSON format
- `test_results_*.json` - Individual pytest JSON reports

### Report Contents
- Test execution summary (passed/failed/skipped counts)
- Service availability status
- Performance metrics and benchmarks
- Error details and stack traces
- Plugin processing statistics
- Workflow execution times

## Troubleshooting

### Common Issues

1. **Services Not Available**
   ```
   Error: Shortage service not available at http://localhost:6970
   ```
   - Ensure shortage-index service is running on port 6970
   - Check service health endpoints: `/health` or `/`

2. **Plugin Loading Errors**
   ```
   Error: Could not load plugin from file
   ```
   - Check plugin file syntax and imports
   - Ensure plugin inherits from OrchestrationPlugin
   - Verify all required methods are implemented

3. **Database Connection Issues**
   ```
   Error: Could not create MySQL agent
   ```
   - Check MySQL connection configuration
   - Ensure database contains test data
   - Verify database credentials and permissions

4. **Test Timeouts**
   ```
   Error: Test execution timed out
   ```
   - Increase timeout values in test configuration
   - Check for network connectivity issues
   - Verify services are responding normally

### Debug Mode
Run tests with verbose output and debug logging:
```bash
pytest tests/test_orchestration/ -v -s --log-cli-level=DEBUG
```

### Service Health Check
Verify services are running:
```bash
curl http://localhost:6970/health
curl http://localhost:6971/health
```

## Performance Expectations

### Typical Execution Times
- Plugin Manager Tests: 10-30 seconds
- Integration Tests: 20-60 seconds
- End-to-End Tests: 60-180 seconds (depends on service response times)
- Error Handling Tests: 30-90 seconds
- Complete Test Suite: 3-8 minutes

### Performance Benchmarks
- Individual workflow execution: < 45 seconds
- Plugin processing overhead: < 100ms
- Memory usage increase: < 100MB
- Concurrent workflow execution: < 90 seconds

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Use appropriate pytest markers for categorization
3. Include both positive and negative test cases
4. Add performance monitoring for resource-intensive tests
5. Update test data files with new scenarios
6. Document any new dependencies or setup requirements

## Integration with CI/CD

The test suite is designed for integration with continuous integration systems:

```yaml
# Example GitHub Actions workflow
- name: Run Orchestration Tests
  run: |
    # Start required services
    docker-compose up -d shortage-service alert-service mysql
    
    # Wait for services to be ready
    ./wait-for-services.sh
    
    # Run tests
    cd tests/test_orchestration
    python run_orchestration_tests.py
    
    # Upload test results
    upload-artifacts orchestration_test_report.json
```
