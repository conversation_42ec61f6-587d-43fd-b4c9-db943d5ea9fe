"""Comprehensive error handling and propagation testing following TDD principles.

Tests agent-level error handling, workflow-level error propagation, graceful degradation,
network/API failure handling, and context recovery mechanisms.
"""

import asyncio
import pytest
import pytest_asyncio
from typing import Dict, Any, List, Optional

# Add the parent directory to sys.path for imports
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from unittest.mock import AsyncMock, patch, MagicMock
from contextlib import asynccontextmanager

from orchestrator.orchestration_runner import OrchestrationRunner
from orchestrator.financial_orchestrator import FinancialOrchestrator
from orchestrator.context_manager import ContextManager, FinancialWorkflowContext
from orchestrator.exceptions import (
    OrchestrationError,
    AgentExecutionError,
    ContextError,
    NetworkTimeoutError,
    ServiceUnavailableError
)
from agents.base_agent_wrapper import BaseAgentWrapper
from agents.alert_manager_agent import AlertManagerAgent


class TestAgentLevelErrorHandling:
    """Test error handling within individual agents."""

    @pytest.fixture
    def mock_failing_mysql_agent(self):
        """Create MySQL agent that simulates various failure modes."""
        agent = AsyncMock(spec=BaseAgentWrapper)

        # Default to database connection failure
        agent.execute_orchestrated.side_effect = AgentExecutionError(
            "Database connection failed",
            agent_name="mysql",
            error_code="DB_CONNECTION_ERROR"
        )

        return agent

    @pytest.fixture
    def mock_failing_shortage_agent(self):
        """Create shortage agent that simulates analysis failures."""
        agent = AsyncMock(spec=BaseAgentWrapper)

        # Default to analysis computation failure
        agent.execute_orchestrated.side_effect = AgentExecutionError(
            "Shortage calculation failed due to invalid data",
            agent_name="shortage",
            error_code="ANALYSIS_ERROR"
        )

        return agent

    @pytest.fixture
    def mock_failing_alert_agent(self):
        """Create alert agent that simulates notification failures."""
        agent = AsyncMock(spec=BaseAgentWrapper)

        # Default to notification delivery failure
        agent.execute_orchestrated.side_effect = AgentExecutionError(
            "Alert notification service unavailable",
            agent_name="alert",
            error_code="NOTIFICATION_ERROR"
        )

        return agent

    @pytest.mark.asyncio
    async def test_mysql_agent_database_connection_errors(
        self,
        mock_failing_mysql_agent
    ):
        """Test MySQL agent handling of database connection failures."""
        # Test connection timeout
        mock_failing_mysql_agent.process_query.side_effect = NetworkTimeoutError(
            "Database connection timed out after 30 seconds",
            timeout_seconds=30.0
        )

        with pytest.raises(NetworkTimeoutError) as exc_info:
            await mock_failing_mysql_agent.process_query("SELECT * FROM orders")

        error = exc_info.value
        assert "timed out" in str(error), "Should indicate timeout error"
        assert error.timeout_seconds == 30.0, "Should include timeout duration"

    @pytest.mark.asyncio
    async def test_mysql_agent_invalid_query_errors(
        self,
        mock_failing_mysql_agent
    ):
        """Test MySQL agent handling of invalid SQL queries."""
        # Test SQL syntax error
        mock_failing_mysql_agent.process_query.side_effect = AgentExecutionError(
            "SQL syntax error: Invalid table name 'invalid_table'",
            agent_name="mysql",
            error_code="SQL_SYNTAX_ERROR"
        )
        
        with pytest.raises(AgentExecutionError) as exc_info:
            await mock_failing_mysql_agent.process_query("SELECT * FROM invalid_table")
        
        error = exc_info.value
        assert error.agent_name == "mysql", "Should identify MySQL agent as source"
        assert error.error_code == "SQL_SYNTAX_ERROR", "Should categorize error type"
        assert "syntax error" in str(error), "Should describe SQL syntax issue"

    @pytest.mark.asyncio
    async def test_shortage_agent_invalid_data_errors(
        self,
        mock_failing_shortage_agent
    ):
        """Test shortage agent handling of invalid input data."""
        # Test malformed input data
        mock_failing_shortage_agent.analyze_shortage.side_effect = AgentExecutionError(
            "Invalid material data: missing required field 'current_stock'",
            agent_name="shortage",
            error_code="INVALID_INPUT_DATA"
        )
        
        invalid_data = {
            'materials': [
                {'code': 'MM2004', 'required_stock': 100}  # Missing current_stock
            ]
        }
        
        with pytest.raises(AgentExecutionError) as exc_info:
            await mock_failing_shortage_agent.analyze_shortage(invalid_data)
        
        error = exc_info.value
        assert error.agent_name == "shortage", "Should identify shortage agent"
        assert "missing required field" in str(error), "Should specify missing field"

    @pytest.mark.asyncio
    async def test_shortage_agent_calculation_errors(
        self,
        mock_failing_shortage_agent
    ):
        """Test shortage agent handling of calculation failures."""
        # Test division by zero or invalid calculations
        mock_failing_shortage_agent.analyze_shortage.side_effect = AgentExecutionError(
            "Shortage index calculation failed: division by zero for material MM2004",
            agent_name="shortage",
            error_code="CALCULATION_ERROR"
        )
        
        problematic_data = {
            'materials': [
                {'code': 'MM2004', 'current_stock': 100, 'required_stock': 0}  # Will cause division issues
            ]
        }
        
        with pytest.raises(AgentExecutionError) as exc_info:
            await mock_failing_shortage_agent.analyze_shortage(problematic_data)
        
        error = exc_info.value
        assert "calculation failed" in str(error), "Should indicate calculation failure"
        assert "MM2004" in str(error), "Should identify problematic material"

    @pytest.mark.asyncio
    async def test_alert_agent_notification_service_errors(
        self,
        mock_failing_alert_agent
    ):
        """Test alert agent handling of notification service failures."""
        # Test email service unavailable
        mock_failing_alert_agent.send_alerts.side_effect = ServiceUnavailableError(
            "Email notification service is temporarily unavailable",
            service_name="email_service",
            retry_after=300  # 5 minutes
        )
        
        alert_data = {
            'alerts': [{'type': 'SHORTAGE_WARNING', 'severity': 'HIGH'}],
            'channels': ['email']
        }
        
        with pytest.raises(ServiceUnavailableError) as exc_info:
            await mock_failing_alert_agent.send_alerts(alert_data)
        
        error = exc_info.value
        assert error.service_name == "email_service", "Should identify failed service"
        assert error.retry_after == 300, "Should include retry timing"

    @pytest.mark.asyncio
    async def test_alert_agent_partial_delivery_failures(
        self,
        mock_failing_alert_agent
    ):
        """Test alert agent handling when some notifications succeed and others fail."""
        # Mock partial success scenario
        mock_failing_alert_agent.send_alerts.return_value = {
            'status': 'partial_success',
            'alerts_sent': [
                {'alert_id': 'ALT-001', 'channel': 'slack', 'status': 'sent'},
                {'alert_id': 'ALT-002', 'channel': 'email', 'status': 'failed', 'error': 'Service unavailable'}
            ],
            'total_attempted': 2,
            'successful': 1,
            'failed': 1,
            'errors': ['Email service unavailable for ALT-002']
        }
        
        alert_data = {
            'alerts': [{'type': 'SHORTAGE_WARNING', 'severity': 'HIGH'}],
            'channels': ['slack', 'email']
        }
        
        result = await mock_failing_alert_agent.send_alerts(alert_data)
        
        # Validate partial success handling
        assert result['status'] == 'partial_success', "Should report partial success"
        assert result['successful'] == 1, "Should count successful deliveries"
        assert result['failed'] == 1, "Should count failed deliveries"
        assert len(result['errors']) > 0, "Should include error details"


class TestWorkflowLevelErrorPropagation:
    """Validate error propagation across the workflow pipeline."""

    @pytest_asyncio.fixture
    async def orchestration_runner_with_failing_agents(self):
        """Create orchestration runner with controllable agent failures."""
        runner = AsyncMock(spec=OrchestrationRunner)
        runner.agents = {
            'mysql': AsyncMock(spec=MySQLAgent),
            'shortage': AsyncMock(spec=ShortageAnalyzerAgent),
            'alert': AsyncMock(spec=AlertManagerAgent)
        }
        return runner

    @pytest.mark.asyncio
    async def test_mysql_agent_failure_propagation(
        self,
        orchestration_runner_with_failing_agents,
        realistic_financial_queries: List[Dict[str, Any]]
    ):
        """Simulate MySQL agent failures and verify proper error propagation to downstream agents."""
        runner = orchestration_runner_with_failing_agents
        
        # MySQL agent fails
        runner.agents['mysql'].execute_orchestrated.side_effect = AgentExecutionError(
            "Database connection lost",
            agent_name="mysql",
            error_code="CONNECTION_LOST"
        )
        
        # Mock the orchestration runner execution
        runner.execute_financial_query.side_effect = OrchestrationError(
            "Workflow failed due to MySQL agent error: Database connection lost",
            workflow_id="test-workflow-001",
            failed_step="mysql_query",
            original_error=AgentExecutionError("Database connection lost", "mysql", "CONNECTION_LOST")
        )
        
        query_data = realistic_financial_queries[0]
        
        with pytest.raises(OrchestrationError) as exc_info:
            await runner.execute_financial_query(
                query=query_data['query'],
                workflow_type=query_data['expected_workflow']
            )
        
        error = exc_info.value
        assert error.failed_step == "mysql_query", "Should identify failed step"
        assert error.original_error is not None, "Should preserve original error"
        assert isinstance(error.original_error, AgentExecutionError), "Should preserve error type"
        assert error.original_error.agent_name == "mysql", "Should identify source agent"

    @pytest.mark.asyncio
    async def test_shortage_agent_failure_with_mysql_success(
        self,
        orchestration_runner_with_failing_agents
    ):
        """Test workflow when MySQL succeeds but shortage analysis fails."""
        runner = orchestration_runner_with_failing_agents
        
        # MySQL succeeds
        runner.agents['mysql'].execute_orchestrated.return_value = {
            'status': 'success',
            'data': {'materials': [{'code': 'MM2004', 'stock': 100}]}
        }

        # Shortage analysis fails
        runner.agents['shortage'].execute_orchestrated.side_effect = AgentExecutionError(
            "Shortage calculation service is overloaded",
            agent_name="shortage",
            error_code="SERVICE_OVERLOADED"
        )
        
        # Mock orchestration failure at shortage step
        runner.execute_financial_query.side_effect = OrchestrationError(
            "Workflow failed at shortage analysis: Shortage calculation service is overloaded",
            workflow_id="test-workflow-002",
            failed_step="shortage_analysis",
            partial_results={'mysql_results': {'materials': [{'code': 'MM2004', 'stock': 100}]}},
            original_error=AgentExecutionError("Shortage calculation service is overloaded", "shortage", "SERVICE_OVERLOADED")
        )
        
        with pytest.raises(OrchestrationError) as exc_info:
            await runner.execute_financial_query(
                query="Test shortage failure",
                workflow_type="shortage_analysis"
            )
        
        error = exc_info.value
        assert error.failed_step == "shortage_analysis", "Should identify shortage step as failure point"
        assert error.partial_results is not None, "Should preserve MySQL results"
        assert 'mysql_results' in error.partial_results, "Should include MySQL data"

    @pytest.mark.asyncio
    async def test_alert_agent_failure_with_analysis_success(
        self,
        orchestration_runner_with_failing_agents
    ):
        """Test workflow when analysis succeeds but alert delivery fails."""
        runner = orchestration_runner_with_failing_agents
        
        # MySQL and shortage succeed
        runner.agents['mysql'].execute_orchestrated.return_value = {
            'status': 'success',
            'data': {'materials': [{'code': 'MM2004', 'stock': 100}]}
        }

        runner.agents['shortage'].execute_orchestrated.return_value = {
            'status': 'success',
            'shortage_analysis': {'materials': [{'code': 'MM2004', 'shortage_index': 0.8}]}
        }

        # Alert delivery fails
        runner.agents['alert'].execute_orchestrated.side_effect = ServiceUnavailableError(
            "All notification channels are currently unavailable"
        )
        
        # Mock workflow with partial success (alert step is often optional)
        runner.execute_financial_query.return_value = {
            'workflow_id': 'test-workflow-003',
            'status': 'partial_success',
            'results': {
                'mysql_results': {'materials': [{'code': 'MM2004', 'stock': 100}]},
                'shortage_analysis': {'materials': [{'code': 'MM2004', 'shortage_index': 0.8}]},
                'alerts_sent': None
            },
            'warnings': ['Alert delivery failed: All notification channels are currently unavailable'],
            'failed_steps': ['alert_generation'],
            'execution_time': 3.5
        }
        
        result = await runner.execute_financial_query(
            query="Test alert failure",
            workflow_type="shortage_analysis"
        )
        
        # Validate partial success handling
        assert result['status'] == 'partial_success', "Should report partial success"
        assert 'warnings' in result, "Should include warnings about failed steps"
        assert 'failed_steps' in result, "Should list failed steps"
        assert 'alert_generation' in result['failed_steps'], "Should identify alert step failure"
        
        # Core analysis should still be available
        assert result['results']['mysql_results'] is not None, "Should preserve MySQL results"
        assert result['results']['shortage_analysis'] is not None, "Should preserve shortage analysis"

    @pytest.mark.asyncio
    async def test_cascading_failure_propagation(
        self,
        orchestration_runner_with_failing_agents
    ):
        """Test how errors cascade through the entire workflow."""
        runner = orchestration_runner_with_failing_agents
        
        # Simulate cascading failures - MySQL fails, causing all downstream failures
        runner.agents['mysql'].execute_orchestrated.side_effect = AgentExecutionError(
            "Database server is down for maintenance",
            agent_name="mysql",
            error_code="SERVER_MAINTENANCE"
        )
        
        # Mock complete workflow failure
        runner.execute_financial_query.side_effect = OrchestrationError(
            "Complete workflow failure: Cannot proceed without database access",
            workflow_id="test-workflow-004",
            failed_step="mysql_query",
            cascade_failures=["shortage_analysis", "alert_generation"],
            original_error=AgentExecutionError("Database server is down for maintenance", "mysql", "SERVER_MAINTENANCE")
        )
        
        with pytest.raises(OrchestrationError) as exc_info:
            await runner.execute_financial_query(
                query="Test cascading failure",
                workflow_type="comprehensive"
            )
        
        error = exc_info.value
        assert error.failed_step == "mysql_query", "Should identify root failure point"
        
        # Check if cascading failures are tracked
        if hasattr(error, 'cascade_failures'):
            assert len(error.cascade_failures) > 0, "Should track cascading failures"
            assert "shortage_analysis" in error.cascade_failures, "Should include downstream failures"


class TestGracefulDegradation:
    """Test system behavior when agents fail or services are unavailable."""

    @pytest.fixture
    def degraded_orchestration_runner(self):
        """Create orchestration runner configured for graceful degradation."""
        runner = AsyncMock(spec=OrchestrationRunner)
        
        # Configure with graceful degradation options
        runner.configuration = {
            'graceful_degradation': True,
            'optional_steps': ['alert_generation', 'notification'],
            'retry_attempts': 3,
            'retry_delay': 1.0
        }
        
        return runner

    @pytest.mark.asyncio
    async def test_workflow_continuation_with_optional_step_failures(
        self,
        degraded_orchestration_runner
    ):
        """Test workflow can complete successfully even when alert service fails."""
        runner = degraded_orchestration_runner
        
        # Mock workflow that continues despite alert failure
        runner.execute_financial_query.return_value = {
            'workflow_id': 'test-graceful-001',
            'status': 'completed',  # Still completed despite optional failure
            'results': {
                'mysql_results': {'materials': [{'code': 'MM2004', 'stock': 100}]},
                'shortage_analysis': {'overall_risk_score': 0.7, 'materials': []}
            },
            'optional_failures': {
                'alert_generation': 'Service temporarily unavailable'
            },
            'degraded_mode': True,
            'execution_time': 4.2
        }
        
        result = await runner.execute_financial_query(
            query="Test graceful degradation",
            workflow_type="shortage_analysis"
        )
        
        # Validate graceful degradation
        assert result['status'] == 'completed', "Should complete despite optional failures"
        assert result.get('degraded_mode') is True, "Should indicate degraded mode operation"
        assert 'optional_failures' in result, "Should report optional step failures"
        
        # Core functionality should work
        assert 'mysql_results' in result['results'], "Core MySQL functionality should work"
        assert 'shortage_analysis' in result['results'], "Core analysis should work"

    @pytest.mark.asyncio
    async def test_retry_mechanism_for_transient_failures(
        self,
        degraded_orchestration_runner
    ):
        """Test retry mechanisms when agents encounter transient failures."""
        runner = degraded_orchestration_runner
        
        # Mock retry scenario - fail twice, then succeed
        call_count = 0
        def mock_execute_with_retries(query, workflow_type):
            nonlocal call_count
            call_count += 1
            
            if call_count <= 2:
                # First two attempts fail with transient error
                raise NetworkTimeoutError(
                    f"Network timeout on attempt {call_count}",
                    timeout_seconds=10.0
                )
            else:
                # Third attempt succeeds
                return {
                    'workflow_id': f'test-retry-{call_count}',
                    'status': 'completed',
                    'results': {'mysql_results': {'orders': []}},
                    'retry_count': call_count - 1,
                    'execution_time': 5.5
                }
        
        runner.execute_financial_query.side_effect = mock_execute_with_retries
        
        # Should eventually succeed after retries
        result = await runner.execute_financial_query(
            query="Test retry mechanism",
            workflow_type="shortage_analysis"
        )
        
        # Validate retry behavior
        assert result['status'] == 'completed', "Should eventually succeed"
        assert result['retry_count'] == 2, "Should have retried 2 times"
        assert call_count == 3, "Should have made 3 attempts total"

    @pytest.mark.asyncio
    async def test_circuit_breaker_functionality(
        self,
        degraded_orchestration_runner
    ):
        """Test circuit breaker patterns in alert notification system."""
        runner = degraded_orchestration_runner
        
        # Mock circuit breaker behavior
        circuit_open_count = 0
        def mock_circuit_breaker_execution(query, workflow_type):
            nonlocal circuit_open_count
            circuit_open_count += 1
            
            if circuit_open_count <= 3:
                # Circuit breaker opens after repeated failures
                return {
                    'workflow_id': f'test-circuit-{circuit_open_count}',
                    'status': 'partial_success',
                    'results': {
                        'mysql_results': {'materials': []},
                        'shortage_analysis': {'overall_risk_score': 0.5}
                    },
                    'circuit_breaker_status': 'OPEN',
                    'failed_services': ['alert_notification'],
                    'execution_time': 2.1
                }
            else:
                # Circuit breaker allows retry after timeout
                return {
                    'workflow_id': f'test-circuit-{circuit_open_count}',
                    'status': 'completed',
                    'results': {
                        'mysql_results': {'materials': []},
                        'shortage_analysis': {'overall_risk_score': 0.5},
                        'alerts_sent': {'count': 1}
                    },
                    'circuit_breaker_status': 'CLOSED',
                    'execution_time': 3.8
                }
        
        runner.execute_financial_query.side_effect = mock_circuit_breaker_execution
        
        # Test multiple executions to trigger circuit breaker
        results = []
        for i in range(4):
            result = await runner.execute_financial_query(
                query=f"Test circuit breaker {i}",
                workflow_type="comprehensive"
            )
            results.append(result)
        
        # Validate circuit breaker behavior
        # First 3 should have open circuit breaker
        for i in range(3):
            assert results[i]['circuit_breaker_status'] == 'OPEN', f"Circuit should be open for attempt {i}"
            assert 'alert_notification' in results[i]['failed_services'], "Alert service should be marked as failed"
        
        # 4th should have closed circuit breaker (recovery)
        assert results[3]['circuit_breaker_status'] == 'CLOSED', "Circuit should close after timeout"
        assert results[3]['status'] == 'completed', "Should complete fully after circuit closes"

    @pytest.mark.asyncio
    async def test_fallback_data_sources(
        self,
        degraded_orchestration_runner
    ):
        """Test system can use fallback data sources when primary sources fail."""
        runner = degraded_orchestration_runner
        
        # Mock fallback scenario
        runner.execute_financial_query.return_value = {
            'workflow_id': 'test-fallback-001',
            'status': 'completed',
            'results': {
                'mysql_results': None,  # Primary database failed
                'fallback_results': {  # Used cached/backup data
                    'source': 'cached_data',
                    'timestamp': '2024-11-01T09:00:00Z',
                    'materials': [{'code': 'MM2004', 'stock': 100, 'cached': True}]
                },
                'shortage_analysis': {'overall_risk_score': 0.6}
            },
            'fallback_mode': True,
            'data_freshness': 'stale',
            'execution_time': 1.8
        }
        
        result = await runner.execute_financial_query(
            query="Test fallback data sources",
            workflow_type="shortage_analysis"
        )
        
        # Validate fallback behavior
        assert result['status'] == 'completed', "Should complete using fallback data"
        assert result.get('fallback_mode') is True, "Should indicate fallback mode"
        assert result.get('data_freshness') == 'stale', "Should indicate data staleness"
        
        # Should have fallback results instead of primary
        assert result['results']['mysql_results'] is None, "Primary source should be None"
        assert 'fallback_results' in result['results'], "Should include fallback data"
        assert result['results']['fallback_results']['source'] == 'cached_data', "Should identify fallback source"


class TestNetworkAndAPIFailureHandling:
    """Test handling of network failures and API timeouts."""

    @pytest.mark.asyncio
    async def test_connection_timeout_handling(
        self,
        mock_orchestration_runner
    ):
        """Simulate network timeouts and validate retry mechanisms."""
        # Mock timeout scenarios with exponential backoff
        timeout_attempts = []
        
        def mock_timeout_execution(query, workflow_type):
            attempt_count = len(timeout_attempts) + 1
            timeout_attempts.append(attempt_count)
            
            if attempt_count <= 2:
                # First two attempts timeout
                raise NetworkTimeoutError(
                    f"Connection timeout after {attempt_count * 10} seconds",
                    timeout_seconds=attempt_count * 10.0
                )
            else:
                # Third attempt succeeds
                return {
                    'workflow_id': 'test-timeout-recovery',
                    'status': 'completed',
                    'results': {'mysql_results': {'orders': []}},
                    'retry_attempts': len(timeout_attempts) - 1,
                    'total_timeout_duration': sum(range(1, len(timeout_attempts))) * 10
                }
        
        mock_orchestration_runner.execute_financial_query.side_effect = mock_timeout_execution
        
        result = await mock_orchestration_runner.execute_financial_query(
            query="Test timeout handling",
            workflow_type="shortage_analysis"
        )
        
        # Validate timeout handling
        assert result['status'] == 'completed', "Should eventually succeed after timeouts"
        assert result['retry_attempts'] == 2, "Should have retried twice"
        assert len(timeout_attempts) == 3, "Should have made 3 total attempts"

    @pytest.mark.asyncio
    async def test_api_rate_limiting_handling(
        self,
        mock_orchestration_runner
    ):
        """Test handling of API rate limiting and backoff strategies."""
        # Mock rate limiting scenarios
        rate_limit_count = 0
        
        def mock_rate_limited_execution(query, workflow_type):
            nonlocal rate_limit_count
            rate_limit_count += 1
            
            if rate_limit_count <= 2:
                # First two attempts hit rate limit
                raise ServiceUnavailableError(
                    f"API rate limit exceeded (attempt {rate_limit_count})",
                    service_name="mcp_service"
                )
            else:
                # Third attempt succeeds
                return {
                    'workflow_id': 'test-rate-limit-recovery',
                    'status': 'completed',
                    'results': {'shortage_analysis': {'overall_risk_score': 0.4}},
                    'rate_limit_retries': rate_limit_count - 1,
                    'execution_time': 8.5
                }
        
        mock_orchestration_runner.execute_financial_query.side_effect = mock_rate_limited_execution
        
        result = await mock_orchestration_runner.execute_financial_query(
            query="Test rate limiting",
            workflow_type="shortage_analysis"
        )
        
        # Validate rate limiting handling
        assert result['status'] == 'completed', "Should succeed after rate limit backoff"
        assert result['rate_limit_retries'] == 2, "Should track rate limit retries"
        assert rate_limit_count == 3, "Should have made 3 attempts"

    @pytest.mark.asyncio
    async def test_service_discovery_failures(
        self,
        mock_orchestration_runner
    ):
        """Test handling when services cannot be discovered or reached."""
        # Mock service discovery failure
        mock_orchestration_runner.execute_financial_query.side_effect = ServiceUnavailableError(
            "Unable to discover MCP services: DNS resolution failed",
            service_name="service_discovery"
        )
        
        with pytest.raises(ServiceUnavailableError) as exc_info:
            await mock_orchestration_runner.execute_financial_query(
                query="Test service discovery failure",
                workflow_type="shortage_analysis"
            )
        
        error = exc_info.value
        assert "discovery" in str(error), "Should indicate service discovery issue"
        assert error.service_name == "service_discovery", "Should identify discovery service"
        assert error.retry_after is None, "Should indicate no retry is possible"


class TestContextRecoveryMechanisms:
    """Validate context recovery from persisted state."""

    @pytest_asyncio.fixture
    async def context_manager_with_persistence(self):
        """Create context manager with persistence for recovery testing."""
        manager = ContextManager()
        # Enable persistence for testing
        manager._enable_persistence = True
        yield manager
        # Cleanup
        for workflow_id in list(manager.active_contexts.keys()):
            manager.cleanup_context(workflow_id)

    @pytest.mark.asyncio
    async def test_context_corruption_recovery(
        self,
        context_manager_with_persistence: ContextManager
    ):
        """Simulate context data corruption and test recovery mechanisms."""
        # Store valid context
        valid_context = FinancialWorkflowContext(
            workflow_id='test-corruption-001',
            original_query='Test context corruption',
            query_type='shortage_analysis',
            workflow_pattern='shortage_analysis'
        )
        
        # Store context using the actual method
        context_manager_with_persistence.active_contexts[valid_context.workflow_id] = valid_context

        # Simulate context corruption by modifying stored data
        with patch.object(context_manager_with_persistence, 'load_context') as mock_load:
            # Mock corrupted data - return None to simulate corruption
            mock_load.return_value = None

            # Should handle corruption gracefully
            recovered_context = context_manager_with_persistence.get_context('test-corruption-001')
            assert recovered_context is None, "Should return None for corrupted context"

    @pytest.mark.asyncio
    async def test_partial_context_recovery(
        self,
        context_manager_with_persistence: ContextManager
    ):
        """Test recovery when only partial context data is available."""
        # Store partial context (simulating interrupted workflow)
        partial_context = FinancialWorkflowContext(
            workflow_id='test-partial-001',
            original_query='Test partial recovery',
            query_type='shortage_analysis',
            workflow_pattern='shortage_analysis',
            current_step=2  # Workflow was interrupted at step 2
        )
        
        # Store context using the actual method
        context_manager_with_persistence.active_contexts[partial_context.workflow_id] = partial_context

        # Should recover partial context successfully
        recovered_context = context_manager_with_persistence.get_context('test-partial-001')

        assert recovered_context is not None, "Should recover partial context"
        assert recovered_context.workflow_id == 'test-partial-001', "Should preserve workflow ID"
        assert recovered_context.current_step == 2, "Should preserve current step"

    @pytest.mark.asyncio
    async def test_context_backup_and_restore(
        self,
        context_manager_with_persistence: ContextManager
    ):
        """Test context backup creation and restoration."""
        # Store multiple contexts
        contexts = []
        for i in range(3):
            context = FinancialWorkflowContext(
                workflow_id=f'test-backup-{i:03d}',
                original_query=f'Test backup scenario {i}',
                query_type='shortage_analysis',
                workflow_pattern='shortage_analysis'
            )
            contexts.append(context)
            context_manager_with_persistence.active_contexts[context.workflow_id] = context

        # Create backup (simulate by copying active contexts)
        backup_data = context_manager_with_persistence.active_contexts.copy()

        # Simulate data loss by clearing contexts
        context_manager_with_persistence.active_contexts.clear()

        # Verify contexts are gone
        for context in contexts:
            recovered = context_manager_with_persistence.get_context(context.workflow_id)
            assert recovered is None, f"Context {context.workflow_id} should be cleared"

        # Restore from backup
        context_manager_with_persistence.active_contexts.update(backup_data)
        
        # Verify contexts are restored
        for original_context in contexts:
            restored_context = await context_manager_with_persistence.get_context(original_context.workflow_id)
            assert restored_context is not None, f"Context {original_context.workflow_id} should be restored"
            assert restored_context.user_query == original_context.user_query, "Should preserve query data"
            assert restored_context.entities == original_context.entities, "Should preserve entities"

    @pytest.mark.asyncio
    async def test_concurrent_context_corruption_handling(
        self,
        context_manager_with_persistence: ContextManager
    ):
        """Test context recovery under concurrent corruption scenarios."""
        # Create multiple contexts concurrently
        contexts = []
        for i in range(5):
            context = FinancialWorkflowContext(
                workflow_id=f'test-concurrent-corruption-{i:02d}',
                original_query=f'Concurrent test {i}',
                query_type='shortage_analysis',
                workflow_pattern='shortage_analysis'
            )
            contexts.append(context)
        
        # Store contexts concurrently
        for ctx in contexts:
            context_manager_with_persistence.active_contexts[ctx.workflow_id] = ctx

        # Simulate concurrent corruption attempts by removing some contexts
        for ctx in contexts[:3]:  # Corrupt first 3
            if ctx.workflow_id in context_manager_with_persistence.active_contexts:
                del context_manager_with_persistence.active_contexts[ctx.workflow_id]
        
        # Attempt to recover all contexts
        recovery_results = []
        for context in contexts:
            try:
                recovered = await context_manager_with_persistence.get_context(context.workflow_id)
                recovery_results.append((context.workflow_id, recovered is not None))
            except ContextError:
                recovery_results.append((context.workflow_id, False))
        
        # At least some contexts should be recoverable
        successful_recoveries = [result for ctx_id, result in recovery_results if result]
        assert len(successful_recoveries) >= 2, "Should recover at least some uncorrupted contexts"