{"workflow_id": "workflow_7e257240", "original_query": "Comprehensive supply chain analysis for work orders WO-202506001, WO-202506002, WO-202506003: Query database for all component inventory levels including CPUs, GPUs, Memory, Storage. Calculate weighted shortage indices based on actual database quantities versus order requirements.", "query_type": "inventory_check", "workflow_pattern": "mysql_shortage", "mysql_context": null, "shortage_context": null, "alert_context": null, "metadata": {"created_at": "2025-08-28T11:15:45.111786", "updated_at": "2025-08-28T11:15:45.111787", "version": "1.0", "workflow_id": "workflow_7e257240", "query_hash": "5fa0a18c", "agent_executions": []}, "current_step": 0, "total_steps": 3, "is_complete": false, "errors": [], "warnings": []}