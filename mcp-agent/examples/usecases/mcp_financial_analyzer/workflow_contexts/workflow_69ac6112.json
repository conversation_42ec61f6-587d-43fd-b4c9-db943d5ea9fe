{"workflow_id": "workflow_69ac6112", "original_query": "Analyze shortage risk for customer order CUSTORD-202506001: Check database inventory for MM2004 GPUs (require 80 units), DEP9005 CPUs (require 250 units), MEM1024 Memory (require 1200 units). Calculate shortage index based on actual database quantities.", "query_type": "shortage_analysis", "workflow_pattern": "mysql_shortage", "mysql_context": null, "shortage_context": null, "alert_context": null, "metadata": {"created_at": "2025-08-27T14:58:09.855921", "updated_at": "2025-08-27T14:58:09.855922", "version": "1.0", "workflow_id": "workflow_69ac6112", "query_hash": "386aa672", "agent_executions": []}, "current_step": 0, "total_steps": 3, "is_complete": false, "errors": [], "warnings": []}